"""Classifier Agent for format and intent classification."""

import logging
from typing import Dict, Any, Optional

from agents.base_agent import BaseAgent
from utils.llm_client import llm_client
from config import config

class ClassifierAgent(BaseAgent):
    """
    Central classifier agent that determines format and intent,
    then routes to appropriate specialized agents.
    """
    
    def __init__(self):
        super().__init__("classifier")
        self.logger = logging.getLogger("agent.classifier")
    
    def process(self, input_data: Dict[str, Any], thread_id: str) -> Dict[str, Any]:
        """
        Classify input format and intent, then route to appropriate agent.
        
        Args:
            input_data: Dictionary containing:
                - content: The raw content to classify
                - filename: Optional filename for context
                - source_type: Optional hint about source type
            thread_id: Thread/conversation ID
            
        Returns:
            Classification results and routing decision
        """
        try:
            if not self.validate_input(input_data):
                return self.handle_error(
                    ValueError("Invalid input: missing required fields"),
                    input_data
                )
            
            content = input_data["content"]
            filename = input_data.get("filename")
            source_type_hint = input_data.get("source_type")
            
            # Perform classification
            classification = self._classify_content(content, filename, source_type_hint)
            
            # Extract common fields
            common_fields = self.extract_common_fields(content)
            
            # Determine routing
            routing_decision = self._determine_routing(classification)
            
            # Prepare result
            result = {
                "success": True,
                "classification": classification,
                "routing": routing_decision,
                "thread_id": thread_id,
                "metadata": {
                    **common_fields,
                    "filename": filename,
                    "source_type_hint": source_type_hint
                }
            }
            
            # Log to memory
            memory_id = self.log_to_memory(
                thread_id=thread_id,
                source_type=classification["format"],
                intent_type=classification["intent"],
                extracted_fields={
                    "classification": classification,
                    "routing_decision": routing_decision,
                    "confidence": classification.get("confidence", 0.0)
                },
                raw_content=content[:500],  # Store first 500 chars
                metadata={
                    "filename": filename,
                    "agent_stage": "classification"
                }
            )
            
            result["memory_id"] = memory_id
            
            self.logger.info(
                f"Classified content: format={classification['format']}, "
                f"intent={classification['intent']}, "
                f"confidence={classification.get('confidence', 'N/A')}, "
                f"routing={routing_decision['target_agent']}"
            )
            
            return result
            
        except Exception as e:
            return self.handle_error(e, input_data)
    
    def _classify_content(self, content: str, filename: Optional[str] = None, 
                         source_type_hint: Optional[str] = None) -> Dict[str, Any]:
        """
        Classify content format and intent using LLM.
        
        Args:
            content: Content to classify
            filename: Optional filename for context
            source_type_hint: Optional hint about source type
            
        Returns:
            Classification results
        """
        # Use LLM for classification
        classification = llm_client.classify_format_and_intent(content, filename)
        
        # Apply source type hint if provided and confidence is low
        if (source_type_hint and 
            source_type_hint in config.FORMAT_TYPES and 
            classification.get("confidence", 0) < 0.7):
            
            self.logger.info(f"Applying source type hint: {source_type_hint}")
            classification["format"] = source_type_hint
            classification["confidence"] = max(classification.get("confidence", 0), 0.6)
        
        # Add additional metadata
        classification.update({
            "content_length": len(content),
            "has_filename": filename is not None,
            "source_hint_applied": source_type_hint is not None
        })
        
        return classification
    
    def _determine_routing(self, classification: Dict[str, Any]) -> Dict[str, Any]:
        """
        Determine which agent should handle the classified content.
        
        Args:
            classification: Classification results
            
        Returns:
            Routing decision
        """
        format_type = classification["format"]
        intent_type = classification["intent"]
        confidence = classification.get("confidence", 0.0)
        
        # Routing logic based on format
        if format_type == "json":
            target_agent = "json_agent"
            priority = "high" if confidence > 0.8 else "medium"
        elif format_type == "email":
            target_agent = "email_agent"
            priority = "high" if confidence > 0.7 else "medium"
        elif format_type == "pdf":
            # Route PDF to appropriate agent based on intent
            if intent_type in ["invoice", "rfq"]:
                target_agent = "json_agent"  # Structure extraction
            else:
                target_agent = "json_agent"  # Default to JSON agent for PDFs
            priority = "medium"
        else:
            # Default to email agent for text content
            target_agent = "email_agent"
            priority = "low" if confidence < 0.5 else "medium"
        
        # Special routing for high-priority intents
        if intent_type in ["complaint", "urgent"]:
            priority = "high"
        
        return {
            "target_agent": target_agent,
            "priority": priority,
            "confidence": confidence,
            "reasoning": f"Format: {format_type}, Intent: {intent_type}",
            "requires_human_review": confidence < 0.3 or intent_type == "complaint"
        }
    
    def get_classification_stats(self, thread_id: str) -> Dict[str, Any]:
        """
        Get classification statistics for a thread.
        
        Args:
            thread_id: Thread to analyze
            
        Returns:
            Statistics about classifications in this thread
        """
        thread_history = self.get_thread_context(thread_id, limit=20)
        
        if not thread_history:
            return {"total_classifications": 0}
        
        # Analyze classification history
        formats = []
        intents = []
        confidences = []
        
        for entry in thread_history:
            if entry.processing_agent == "classifier" and entry.extracted_fields:
                classification = entry.extracted_fields.get("classification", {})
                if classification:
                    formats.append(classification.get("format"))
                    intents.append(classification.get("intent"))
                    confidences.append(classification.get("confidence", 0))
        
        # Calculate statistics
        stats = {
            "total_classifications": len(formats),
            "most_common_format": max(set(formats), key=formats.count) if formats else None,
            "most_common_intent": max(set(intents), key=intents.count) if intents else None,
            "average_confidence": sum(confidences) / len(confidences) if confidences else 0,
            "format_distribution": {fmt: formats.count(fmt) for fmt in set(formats)},
            "intent_distribution": {intent: intents.count(intent) for intent in set(intents)}
        }
        
        return stats
