"""JSON Agent for processing structured JSON data."""

import json
import logging
from typing import Dict, Any, List, Optional
from jsonschema import validate, ValidationError
from datetime import datetime

from agents.base_agent import BaseAgent
from utils.llm_client import llm_client

class JSONAgent(BaseAgent):
    """
    Specialized agent for processing JSON data.
    Handles extraction, validation, and reformatting of structured data.
    """
    
    def __init__(self):
        super().__init__("json_agent")
        self.logger = logging.getLogger("agent.json")
        
        # Define common schemas for different intent types
        self.schemas = {
            "invoice": {
                "type": "object",
                "properties": {
                    "invoice_number": {"type": "string"},
                    "date": {"type": "string"},
                    "vendor": {"type": "string"},
                    "total_amount": {"type": "number"},
                    "currency": {"type": "string"},
                    "line_items": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "description": {"type": "string"},
                                "quantity": {"type": "number"},
                                "unit_price": {"type": "number"},
                                "total": {"type": "number"}
                            }
                        }
                    }
                }
            },
            "rfq": {
                "type": "object",
                "properties": {
                    "rfq_number": {"type": "string"},
                    "requester": {"type": "string"},
                    "due_date": {"type": "string"},
                    "items": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "item_description": {"type": "string"},
                                "quantity": {"type": "number"},
                                "specifications": {"type": "string"}
                            }
                        }
                    }
                }
            },
            "order": {
                "type": "object",
                "properties": {
                    "order_number": {"type": "string"},
                    "customer": {"type": "string"},
                    "order_date": {"type": "string"},
                    "items": {"type": "array"},
                    "total_amount": {"type": "number"},
                    "status": {"type": "string"}
                }
            }
        }
    
    def process(self, input_data: Dict[str, Any], thread_id: str) -> Dict[str, Any]:
        """
        Process JSON data based on intent and extract structured information.
        
        Args:
            input_data: Dictionary containing:
                - content: JSON content (string or dict)
                - intent_type: Classified intent
                - classification: Full classification results
            thread_id: Thread/conversation ID
            
        Returns:
            Processing results with extracted and validated data
        """
        try:
            if not self.validate_input(input_data):
                return self.handle_error(
                    ValueError("Invalid input: missing required fields"),
                    input_data
                )
            
            content = input_data["content"]
            intent_type = input_data.get("intent_type", "other")
            classification = input_data.get("classification", {})
            
            # Parse JSON content if it's a string
            if isinstance(content, str):
                try:
                    json_data = json.loads(content)
                except json.JSONDecodeError as e:
                    return self.handle_error(
                        ValueError(f"Invalid JSON content: {e}"),
                        input_data
                    )
            else:
                json_data = content
            
            # Process the JSON data
            processing_result = self._process_json_data(json_data, intent_type, thread_id)
            
            # Extract common fields
            common_fields = self.extract_common_fields(str(json_data))
            
            # Prepare result
            result = {
                "success": True,
                "intent_type": intent_type,
                "extracted_data": processing_result["extracted_data"],
                "validation_results": processing_result["validation_results"],
                "anomalies": processing_result["anomalies"],
                "reformatted_data": processing_result["reformatted_data"],
                "thread_id": thread_id,
                "metadata": {
                    **common_fields,
                    "original_classification": classification,
                    "schema_used": processing_result.get("schema_used"),
                    "processing_timestamp": datetime.utcnow().isoformat()
                }
            }
            
            # Log to memory
            memory_id = self.log_to_memory(
                thread_id=thread_id,
                source_type="json",
                intent_type=intent_type,
                extracted_fields=processing_result["extracted_data"],
                raw_content=str(json_data)[:1000],
                metadata={
                    "validation_passed": processing_result["validation_results"]["is_valid"],
                    "anomaly_count": len(processing_result["anomalies"]),
                    "agent_stage": "json_processing"
                }
            )
            
            result["memory_id"] = memory_id
            
            self.logger.info(
                f"Processed JSON data: intent={intent_type}, "
                f"valid={processing_result['validation_results']['is_valid']}, "
                f"anomalies={len(processing_result['anomalies'])}"
            )
            
            return result
            
        except Exception as e:
            return self.handle_error(e, input_data)
    
    def _process_json_data(self, json_data: Dict[str, Any], intent_type: str, 
                          thread_id: str) -> Dict[str, Any]:
        """
        Process JSON data based on intent type.
        
        Args:
            json_data: Parsed JSON data
            intent_type: The classified intent
            thread_id: Thread ID for context
            
        Returns:
            Processing results
        """
        # Get appropriate schema
        schema = self.schemas.get(intent_type, self._get_generic_schema())
        
        # Validate against schema
        validation_results = self._validate_json(json_data, schema)
        
        # Extract structured data using LLM if needed
        extracted_data = self._extract_structured_data(json_data, schema, intent_type)
        
        # Detect anomalies
        anomalies = self._detect_anomalies(json_data, extracted_data, intent_type, thread_id)
        
        # Reformat data to target schema
        reformatted_data = self._reformat_to_schema(extracted_data, schema)
        
        return {
            "extracted_data": extracted_data,
            "validation_results": validation_results,
            "anomalies": anomalies,
            "reformatted_data": reformatted_data,
            "schema_used": intent_type if intent_type in self.schemas else "generic"
        }
    
    def _validate_json(self, json_data: Dict[str, Any], schema: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate JSON data against schema.
        
        Args:
            json_data: Data to validate
            schema: JSON schema
            
        Returns:
            Validation results
        """
        try:
            validate(instance=json_data, schema=schema)
            return {
                "is_valid": True,
                "errors": [],
                "warnings": []
            }
        except ValidationError as e:
            return {
                "is_valid": False,
                "errors": [str(e)],
                "warnings": []
            }
        except Exception as e:
            return {
                "is_valid": False,
                "errors": [f"Validation error: {str(e)}"],
                "warnings": []
            }
    
    def _extract_structured_data(self, json_data: Dict[str, Any], schema: Dict[str, Any], 
                                intent_type: str) -> Dict[str, Any]:
        """
        Extract and structure data using LLM assistance.
        
        Args:
            json_data: Raw JSON data
            schema: Target schema
            intent_type: Intent type for context
            
        Returns:
            Extracted structured data
        """
        # If data already matches schema closely, return as-is
        if self._data_matches_schema_structure(json_data, schema):
            return json_data
        
        # Use LLM to extract structured data
        extracted = llm_client.extract_structured_data(
            content=json.dumps(json_data, indent=2),
            schema=schema
        )
        
        # Merge with original data, preferring extracted values
        result = json_data.copy()
        result.update(extracted)
        
        return result
    
    def _detect_anomalies(self, json_data: Dict[str, Any], extracted_data: Dict[str, Any], 
                         intent_type: str, thread_id: str) -> List[Dict[str, Any]]:
        """
        Detect anomalies in the JSON data.
        
        Args:
            json_data: Original JSON data
            extracted_data: Extracted structured data
            intent_type: Intent type
            thread_id: Thread ID for historical context
            
        Returns:
            List of detected anomalies
        """
        anomalies = []
        
        # Check for missing required fields based on intent
        required_fields = self._get_required_fields(intent_type)
        for field in required_fields:
            if field not in extracted_data or extracted_data[field] is None:
                anomalies.append({
                    "type": "missing_field",
                    "field": field,
                    "severity": "high",
                    "message": f"Required field '{field}' is missing or null"
                })
        
        # Check for unusual values based on historical data
        historical_anomalies = self._check_historical_anomalies(extracted_data, intent_type, thread_id)
        anomalies.extend(historical_anomalies)
        
        # Check for data type inconsistencies
        type_anomalies = self._check_data_types(extracted_data, intent_type)
        anomalies.extend(type_anomalies)
        
        return anomalies
    
    def _reformat_to_schema(self, data: Dict[str, Any], schema: Dict[str, Any]) -> Dict[str, Any]:
        """
        Reformat data to match target schema structure.
        
        Args:
            data: Data to reformat
            schema: Target schema
            
        Returns:
            Reformatted data
        """
        reformatted = {}
        
        if "properties" in schema:
            for field, field_schema in schema["properties"].items():
                if field in data:
                    reformatted[field] = self._format_field_value(data[field], field_schema)
                else:
                    reformatted[field] = None
        
        return reformatted
    
    def _format_field_value(self, value: Any, field_schema: Dict[str, Any]) -> Any:
        """Format a field value according to its schema."""
        if value is None:
            return None
        
        field_type = field_schema.get("type", "string")
        
        try:
            if field_type == "number":
                return float(value) if value != "" else None
            elif field_type == "integer":
                return int(float(value)) if value != "" else None
            elif field_type == "boolean":
                if isinstance(value, str):
                    return value.lower() in ["true", "yes", "1"]
                return bool(value)
            elif field_type == "array":
                return list(value) if not isinstance(value, list) else value
            else:  # string or object
                return str(value) if not isinstance(value, (dict, list)) else value
        except (ValueError, TypeError):
            return value  # Return original if conversion fails
    
    def _get_generic_schema(self) -> Dict[str, Any]:
        """Get a generic schema for unknown intent types."""
        return {
            "type": "object",
            "properties": {
                "id": {"type": "string"},
                "type": {"type": "string"},
                "data": {"type": "object"},
                "timestamp": {"type": "string"}
            }
        }
    
    def _get_required_fields(self, intent_type: str) -> List[str]:
        """Get required fields for an intent type."""
        required_map = {
            "invoice": ["invoice_number", "vendor", "total_amount"],
            "rfq": ["rfq_number", "requester", "items"],
            "order": ["order_number", "customer", "items"]
        }
        return required_map.get(intent_type, [])
    
    def _data_matches_schema_structure(self, data: Dict[str, Any], schema: Dict[str, Any]) -> bool:
        """Check if data structure roughly matches schema."""
        if "properties" not in schema:
            return True
        
        schema_fields = set(schema["properties"].keys())
        data_fields = set(data.keys())
        
        # If more than 70% of schema fields are present, consider it a match
        overlap = len(schema_fields.intersection(data_fields))
        return overlap / len(schema_fields) > 0.7 if schema_fields else True
    
    def _check_historical_anomalies(self, data: Dict[str, Any], intent_type: str, 
                                   thread_id: str) -> List[Dict[str, Any]]:
        """Check for anomalies based on historical data."""
        anomalies = []
        
        # Get historical data for comparison
        thread_history = self.get_thread_context(thread_id, limit=10)
        
        # Simple anomaly detection for numerical fields
        if intent_type == "invoice" and "total_amount" in data:
            historical_amounts = []
            for entry in thread_history:
                if (entry.intent_type == "invoice" and 
                    entry.extracted_fields and 
                    "total_amount" in entry.extracted_fields):
                    amount = entry.extracted_fields["total_amount"]
                    if isinstance(amount, (int, float)):
                        historical_amounts.append(amount)
            
            if historical_amounts and data["total_amount"]:
                current_amount = float(data["total_amount"])
                avg_amount = sum(historical_amounts) / len(historical_amounts)
                
                # Flag if current amount is more than 5x the average
                if current_amount > avg_amount * 5:
                    anomalies.append({
                        "type": "unusual_amount",
                        "field": "total_amount",
                        "severity": "medium",
                        "message": f"Amount {current_amount} is unusually high (avg: {avg_amount:.2f})"
                    })
        
        return anomalies
    
    def _check_data_types(self, data: Dict[str, Any], intent_type: str) -> List[Dict[str, Any]]:
        """Check for data type inconsistencies."""
        anomalies = []
        
        # Define expected types for common fields
        type_expectations = {
            "total_amount": (int, float),
            "quantity": (int, float),
            "unit_price": (int, float),
            "date": str,
            "invoice_number": str,
            "order_number": str
        }
        
        for field, expected_types in type_expectations.items():
            if field in data and data[field] is not None:
                if not isinstance(data[field], expected_types):
                    anomalies.append({
                        "type": "type_mismatch",
                        "field": field,
                        "severity": "low",
                        "message": f"Field '{field}' has unexpected type: {type(data[field])}"
                    })
        
        return anomalies
