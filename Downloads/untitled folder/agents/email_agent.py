"""Email Agent for processing email content and extracting metadata."""

import re
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from email.utils import parseaddr

from agents.base_agent import BaseAgent
from utils.llm_client import llm_client

class EmailAgent(BaseAgent):
    """
    Specialized agent for processing email content.
    Extracts sender info, intent, urgency, and formats for CRM usage.
    """
    
    def __init__(self):
        super().__init__("email_agent")
        self.logger = logging.getLogger("agent.email")
        
        # Define urgency keywords
        self.urgency_keywords = {
            "high": [
                "urgent", "asap", "immediately", "emergency", "critical",
                "deadline", "rush", "priority", "escalate", "complaint"
            ],
            "medium": [
                "soon", "important", "needed", "request", "follow up",
                "reminder", "please", "required"
            ],
            "low": [
                "when possible", "no rush", "fyi", "information",
                "update", "status", "casual"
            ]
        }
    
    def process(self, input_data: Dict[str, Any], thread_id: str) -> Dict[str, Any]:
        """
        Process email content and extract relevant information.
        
        Args:
            input_data: Dictionary containing:
                - content: Email content (string)
                - intent_type: Classified intent
                - classification: Full classification results
            thread_id: Thread/conversation ID
            
        Returns:
            Processing results with extracted email metadata
        """
        try:
            if not self.validate_input(input_data):
                return self.handle_error(
                    ValueError("Invalid input: missing required fields"),
                    input_data
                )
            
            content = input_data["content"]
            intent_type = input_data.get("intent_type", "other")
            classification = input_data.get("classification", {})
            
            # Process the email content
            processing_result = self._process_email_content(content, intent_type, thread_id)
            
            # Extract common fields
            common_fields = self.extract_common_fields(content)
            
            # Prepare result
            result = {
                "success": True,
                "intent_type": intent_type,
                "email_metadata": processing_result["email_metadata"],
                "extracted_entities": processing_result["extracted_entities"],
                "urgency_analysis": processing_result["urgency_analysis"],
                "crm_formatted": processing_result["crm_formatted"],
                "action_items": processing_result["action_items"],
                "thread_id": thread_id,
                "metadata": {
                    **common_fields,
                    "original_classification": classification,
                    "processing_timestamp": datetime.utcnow().isoformat()
                }
            }
            
            # Log to memory
            memory_id = self.log_to_memory(
                thread_id=thread_id,
                source_type="email",
                intent_type=intent_type,
                sender=processing_result["email_metadata"].get("sender"),
                topic=processing_result["email_metadata"].get("subject"),
                extracted_fields={
                    "email_metadata": processing_result["email_metadata"],
                    "urgency": processing_result["urgency_analysis"]["level"],
                    "action_required": len(processing_result["action_items"]) > 0
                },
                raw_content=content[:1000],
                metadata={
                    "urgency_level": processing_result["urgency_analysis"]["level"],
                    "action_item_count": len(processing_result["action_items"]),
                    "agent_stage": "email_processing"
                }
            )
            
            result["memory_id"] = memory_id
            
            self.logger.info(
                f"Processed email: intent={intent_type}, "
                f"urgency={processing_result['urgency_analysis']['level']}, "
                f"actions={len(processing_result['action_items'])}, "
                f"sender={processing_result['email_metadata'].get('sender', 'unknown')}"
            )
            
            return result
            
        except Exception as e:
            return self.handle_error(e, input_data)
    
    def _process_email_content(self, content: str, intent_type: str, 
                              thread_id: str) -> Dict[str, Any]:
        """
        Process email content and extract all relevant information.
        
        Args:
            content: Email content
            intent_type: Classified intent
            thread_id: Thread ID for context
            
        Returns:
            Comprehensive processing results
        """
        # Extract basic email metadata
        email_metadata = self._extract_email_metadata(content)
        
        # Extract entities (names, companies, dates, etc.)
        extracted_entities = self._extract_entities(content)
        
        # Analyze urgency
        urgency_analysis = self._analyze_urgency(content, email_metadata)
        
        # Format for CRM
        crm_formatted = self._format_for_crm(
            email_metadata, extracted_entities, urgency_analysis, intent_type
        )
        
        # Extract action items
        action_items = self._extract_action_items(content, intent_type)
        
        return {
            "email_metadata": email_metadata,
            "extracted_entities": extracted_entities,
            "urgency_analysis": urgency_analysis,
            "crm_formatted": crm_formatted,
            "action_items": action_items
        }
    
    def _extract_email_metadata(self, content: str) -> Dict[str, Any]:
        """
        Extract basic email metadata from content.
        
        Args:
            content: Email content
            
        Returns:
            Email metadata dictionary
        """
        metadata = {
            "sender": None,
            "sender_email": None,
            "recipient": None,
            "subject": None,
            "date": None,
            "body": None
        }
        
        lines = content.split('\n')
        body_start = 0
        
        # Parse email headers
        for i, line in enumerate(lines):
            line = line.strip()
            
            if line.lower().startswith('from:'):
                from_line = line[5:].strip()
                name, email = parseaddr(from_line)
                metadata["sender"] = name if name else email
                metadata["sender_email"] = email
                
            elif line.lower().startswith('to:'):
                metadata["recipient"] = line[3:].strip()
                
            elif line.lower().startswith('subject:'):
                metadata["subject"] = line[8:].strip()
                
            elif line.lower().startswith('date:'):
                metadata["date"] = line[5:].strip()
                
            elif line == '' and i > 0:  # Empty line indicates start of body
                body_start = i + 1
                break
        
        # Extract body
        if body_start < len(lines):
            metadata["body"] = '\n'.join(lines[body_start:]).strip()
        else:
            metadata["body"] = content  # Fallback if no headers found
        
        # Use LLM for additional analysis if basic parsing fails
        if not metadata["sender"] and not metadata["subject"]:
            llm_analysis = llm_client.analyze_email_content(content)
            metadata.update({
                "sender": llm_analysis.get("sender"),
                "subject": llm_analysis.get("subject"),
                "llm_enhanced": True
            })
        
        return metadata
    
    def _extract_entities(self, content: str) -> Dict[str, List[str]]:
        """
        Extract entities like names, companies, dates, etc.
        
        Args:
            content: Email content
            
        Returns:
            Dictionary of extracted entities
        """
        entities = {
            "emails": [],
            "phone_numbers": [],
            "dates": [],
            "companies": [],
            "amounts": [],
            "urls": []
        }
        
        # Email addresses
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        entities["emails"] = re.findall(email_pattern, content)
        
        # Phone numbers (basic patterns)
        phone_pattern = r'(\+?1[-.\s]?)?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})'
        phone_matches = re.findall(phone_pattern, content)
        entities["phone_numbers"] = [''.join(match) for match in phone_matches]
        
        # URLs
        url_pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        entities["urls"] = re.findall(url_pattern, content)
        
        # Monetary amounts
        amount_pattern = r'\$[\d,]+\.?\d*|\d+\.\d{2}\s*(USD|EUR|GBP|dollars?)'
        entities["amounts"] = re.findall(amount_pattern, content, re.IGNORECASE)
        
        # Dates (basic patterns)
        date_patterns = [
            r'\d{1,2}/\d{1,2}/\d{4}',
            r'\d{1,2}-\d{1,2}-\d{4}',
            r'\b\d{1,2}\s+(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{4}\b'
        ]
        for pattern in date_patterns:
            entities["dates"].extend(re.findall(pattern, content, re.IGNORECASE))
        
        # Company names (heuristic: capitalized words ending with Inc, LLC, Corp, etc.)
        company_pattern = r'\b[A-Z][a-zA-Z\s&]+(?:Inc|LLC|Corp|Corporation|Ltd|Limited|Co)\b'
        entities["companies"] = re.findall(company_pattern, content)
        
        return entities
    
    def _analyze_urgency(self, content: str, email_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        Analyze urgency level of the email.
        
        Args:
            content: Email content
            email_metadata: Email metadata
            
        Returns:
            Urgency analysis results
        """
        content_lower = content.lower()
        subject_lower = (email_metadata.get("subject") or "").lower()
        
        # Count urgency keywords
        urgency_scores = {"high": 0, "medium": 0, "low": 0}
        matched_keywords = []
        
        for level, keywords in self.urgency_keywords.items():
            for keyword in keywords:
                # Weight subject line matches higher
                subject_matches = subject_lower.count(keyword)
                content_matches = content_lower.count(keyword)
                
                urgency_scores[level] += subject_matches * 2 + content_matches
                if subject_matches > 0 or content_matches > 0:
                    matched_keywords.append((keyword, level))
        
        # Determine overall urgency level
        if urgency_scores["high"] > 0:
            level = "high"
        elif urgency_scores["medium"] > urgency_scores["low"]:
            level = "medium"
        else:
            level = "low"
        
        # Additional urgency indicators
        urgency_indicators = []
        
        # Check for deadline mentions
        if re.search(r'\b(deadline|due|expires?|urgent|asap)\b', content_lower):
            urgency_indicators.append("deadline_mentioned")
            if level == "low":
                level = "medium"
        
        # Check for complaint language
        if re.search(r'\b(complaint|dissatisfied|unhappy|problem|issue)\b', content_lower):
            urgency_indicators.append("complaint_language")
            level = "high"
        
        return {
            "level": level,
            "scores": urgency_scores,
            "matched_keywords": matched_keywords,
            "indicators": urgency_indicators,
            "confidence": min(max(urgency_scores[level] / 10, 0.1), 1.0)
        }
    
    def _format_for_crm(self, email_metadata: Dict[str, Any], entities: Dict[str, List[str]], 
                       urgency: Dict[str, Any], intent_type: str) -> Dict[str, Any]:
        """
        Format extracted information for CRM usage.
        
        Args:
            email_metadata: Email metadata
            entities: Extracted entities
            urgency: Urgency analysis
            intent_type: Intent type
            
        Returns:
            CRM-formatted data
        """
        return {
            "contact_info": {
                "name": email_metadata.get("sender"),
                "email": email_metadata.get("sender_email"),
                "phone": entities["phone_numbers"][0] if entities["phone_numbers"] else None,
                "company": entities["companies"][0] if entities["companies"] else None
            },
            "communication": {
                "type": "email",
                "subject": email_metadata.get("subject"),
                "date": email_metadata.get("date"),
                "urgency": urgency["level"],
                "intent": intent_type,
                "summary": self._generate_summary(email_metadata.get("body", ""))
            },
            "business_data": {
                "mentioned_amounts": entities["amounts"],
                "mentioned_dates": entities["dates"],
                "mentioned_companies": entities["companies"],
                "urls": entities["urls"]
            },
            "follow_up": {
                "required": urgency["level"] in ["high", "medium"],
                "priority": urgency["level"],
                "suggested_response_time": self._suggest_response_time(urgency["level"])
            }
        }
    
    def _extract_action_items(self, content: str, intent_type: str) -> List[Dict[str, Any]]:
        """
        Extract action items from email content.
        
        Args:
            content: Email content
            intent_type: Intent type
            
        Returns:
            List of action items
        """
        action_items = []
        
        # Look for explicit action words
        action_patterns = [
            r'please\s+([^.!?]+)',
            r'can you\s+([^.!?]+)',
            r'need to\s+([^.!?]+)',
            r'should\s+([^.!?]+)',
            r'must\s+([^.!?]+)',
            r'action required:?\s*([^.!?]+)'
        ]
        
        for pattern in action_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                action_items.append({
                    "action": match.strip(),
                    "type": "explicit",
                    "priority": "medium"
                })
        
        # Intent-specific action items
        if intent_type == "rfq":
            action_items.append({
                "action": "Prepare and send quotation",
                "type": "intent_based",
                "priority": "high"
            })
        elif intent_type == "complaint":
            action_items.append({
                "action": "Investigate and respond to complaint",
                "type": "intent_based",
                "priority": "high"
            })
        elif intent_type == "invoice":
            action_items.append({
                "action": "Process invoice for payment",
                "type": "intent_based",
                "priority": "medium"
            })
        
        return action_items
    
    def _generate_summary(self, body: str) -> str:
        """Generate a brief summary of the email body."""
        if not body or len(body) < 50:
            return body
        
        # Simple extractive summary - first sentence and any sentence with key terms
        sentences = re.split(r'[.!?]+', body)
        if not sentences:
            return body[:100] + "..." if len(body) > 100 else body
        
        summary_parts = [sentences[0].strip()]
        
        # Add sentences with important keywords
        important_keywords = ["request", "need", "urgent", "deadline", "please", "important"]
        for sentence in sentences[1:]:
            if any(keyword in sentence.lower() for keyword in important_keywords):
                summary_parts.append(sentence.strip())
                break
        
        summary = ". ".join(summary_parts)
        return summary[:200] + "..." if len(summary) > 200 else summary
    
    def _suggest_response_time(self, urgency_level: str) -> str:
        """Suggest appropriate response time based on urgency."""
        response_times = {
            "high": "Within 2 hours",
            "medium": "Within 24 hours", 
            "low": "Within 3 business days"
        }
        return response_times.get(urgency_level, "Within 24 hours")
