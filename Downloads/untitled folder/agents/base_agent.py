"""Base agent class for the multi-agent system."""

import logging
import hashlib
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
from datetime import datetime

from shared_memory import memory, MemoryEntry

class BaseAgent(ABC):
    """Abstract base class for all agents in the system."""
    
    def __init__(self, agent_name: str):
        self.agent_name = agent_name
        self.logger = logging.getLogger(f"agent.{agent_name}")
        
    @abstractmethod
    def process(self, input_data: Dict[str, Any], thread_id: str) -> Dict[str, Any]:
        """
        Process input data and return results.
        
        Args:
            input_data: The data to process
            thread_id: Thread/conversation ID for context
            
        Returns:
            Dictionary containing processing results
        """
        pass
    
    def log_to_memory(self, 
                     thread_id: str,
                     source_type: str,
                     intent_type: str,
                     extracted_fields: Dict[str, Any],
                     sender: Optional[str] = None,
                     topic: Optional[str] = None,
                     raw_content: Optional[str] = None,
                     metadata: Optional[Dict[str, Any]] = None) -> str:
        """
        Log processing results to shared memory.
        
        Returns:
            Memory entry ID
        """
        # Create content hash if raw content provided
        content_hash = None
        if raw_content:
            content_hash = hashlib.md5(raw_content.encode()).hexdigest()
        
        # Create memory entry
        entry = MemoryEntry(
            thread_id=thread_id,
            source_type=source_type,
            intent_type=intent_type,
            sender=sender,
            topic=topic,
            extracted_fields=extracted_fields,
            raw_content_hash=content_hash,
            processing_agent=self.agent_name,
            metadata=metadata
        )
        
        # Store in memory
        memory_id = memory.store_memory(entry)
        
        self.logger.info(f"Logged processing result to memory: {memory_id}")
        return memory_id
    
    def get_thread_context(self, thread_id: str, limit: int = 5) -> list:
        """Get recent context for a thread."""
        return memory.get_thread_history(thread_id, limit)
    
    def validate_input(self, input_data: Dict[str, Any]) -> bool:
        """
        Validate input data format.
        
        Args:
            input_data: Data to validate
            
        Returns:
            True if valid, False otherwise
        """
        required_fields = ["content", "source_type"]
        return all(field in input_data for field in required_fields)
    
    def extract_common_fields(self, content: str) -> Dict[str, Any]:
        """
        Extract common fields that all agents might need.
        
        Args:
            content: Raw content to analyze
            
        Returns:
            Dictionary of common extracted fields
        """
        return {
            "content_length": len(content),
            "processing_timestamp": datetime.utcnow().isoformat(),
            "agent_name": self.agent_name
        }
    
    def handle_error(self, error: Exception, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Handle processing errors gracefully.
        
        Args:
            error: The exception that occurred
            input_data: The input that caused the error
            
        Returns:
            Error response dictionary
        """
        error_msg = f"Error in {self.agent_name}: {str(error)}"
        self.logger.error(error_msg)
        
        return {
            "success": False,
            "error": error_msg,
            "agent": self.agent_name,
            "input_summary": {
                "source_type": input_data.get("source_type", "unknown"),
                "content_length": len(str(input_data.get("content", "")))
            }
        }
