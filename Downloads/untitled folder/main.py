"""Main orchestrator for the multi-agent AI system."""

import logging
import uuid
from typing import Dict, Any, Optional, Union, List
from pathlib import Path

from config import config
from shared_memory import memory
from agents.classifier_agent import ClassifierAgent
from agents.json_agent import JSONAgent
from agents.email_agent import EmailAgent
from utils.file_handlers import file_handler

# Configure logging
logging.basicConfig(
    level=getattr(logging, config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(config.LOG_FILE),
        logging.StreamHandler()
    ]
)

class MultiAgentOrchestrator:
    """
    Main orchestrator that coordinates all agents in the system.
    Handles input processing, agent routing, and result aggregation.
    """

    def __init__(self):
        self.logger = logging.getLogger("orchestrator")

        # Initialize agents
        self.classifier = ClassifierAgent()
        self.json_agent = JSONAgent()
        self.email_agent = EmailAgent()

        # Agent routing map
        self.agent_map = {
            "json_agent": self.json_agent,
            "email_agent": self.email_agent
        }

        self.logger.info("Multi-agent orchestrator initialized")

    def process_input(self, input_data: Union[str, Dict[str, Any]],
                     thread_id: Optional[str] = None,
                     source_hint: Optional[str] = None) -> Dict[str, Any]:
        """
        Process input through the multi-agent system.

        Args:
            input_data: Input to process (file path, content string, or dict)
            thread_id: Optional thread ID for conversation tracking
            source_hint: Optional hint about the source type

        Returns:
            Complete processing results
        """
        # Generate thread ID if not provided
        if not thread_id:
            thread_id = str(uuid.uuid4())

        try:
            # Prepare input for classification
            prepared_input = self._prepare_input(input_data, source_hint)

            self.logger.info(f"Processing input for thread {thread_id}")

            # Step 1: Classify the input
            classification_result = self.classifier.process(prepared_input, thread_id)

            if not classification_result["success"]:
                return classification_result

            # Step 2: Route to appropriate agent
            routing_result = self._route_to_agent(
                classification_result, prepared_input, thread_id
            )

            # Step 3: Combine results
            final_result = self._combine_results(classification_result, routing_result, thread_id)

            self.logger.info(f"Successfully processed input for thread {thread_id}")
            return final_result

        except Exception as e:
            error_result = {
                "success": False,
                "error": f"Orchestrator error: {str(e)}",
                "thread_id": thread_id,
                "stage": "orchestration"
            }
            self.logger.error(f"Error processing input: {e}")
            return error_result

    def process_file(self, file_path: str, thread_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Process a file through the multi-agent system.

        Args:
            file_path: Path to the file to process
            thread_id: Optional thread ID

        Returns:
            Processing results
        """
        try:
            # Extract content from file
            content, detected_format, metadata = file_handler.process_file(file_path)

            # Create input data
            input_data = {
                "content": content,
                "filename": Path(file_path).name,
                "source_type": detected_format,
                "file_metadata": metadata
            }

            return self.process_input(input_data, thread_id, detected_format)

        except Exception as e:
            return {
                "success": False,
                "error": f"File processing error: {str(e)}",
                "file_path": file_path,
                "thread_id": thread_id
            }

    def get_thread_summary(self, thread_id: str) -> Dict[str, Any]:
        """
        Get a summary of all processing in a thread.

        Args:
            thread_id: Thread ID to summarize

        Returns:
            Thread summary
        """
        try:
            thread_history = memory.get_thread_history(thread_id, limit=50)

            if not thread_history:
                return {
                    "thread_id": thread_id,
                    "total_entries": 0,
                    "summary": "No entries found for this thread"
                }

            # Analyze thread history
            summary = {
                "thread_id": thread_id,
                "total_entries": len(thread_history),
                "date_range": {
                    "start": thread_history[-1].timestamp.isoformat(),
                    "end": thread_history[0].timestamp.isoformat()
                },
                "agents_used": list(set(entry.processing_agent for entry in thread_history)),
                "source_types": list(set(entry.source_type for entry in thread_history)),
                "intent_types": list(set(entry.intent_type for entry in thread_history)),
                "senders": list(set(entry.sender for entry in thread_history if entry.sender)),
                "topics": list(set(entry.topic for entry in thread_history if entry.topic))
            }

            # Get classification stats
            classification_stats = self.classifier.get_classification_stats(thread_id)
            summary["classification_stats"] = classification_stats

            return summary

        except Exception as e:
            return {
                "thread_id": thread_id,
                "error": f"Error generating summary: {str(e)}"
            }

    def search_by_criteria(self, criteria: Dict[str, Any], limit: int = 10) -> List[Dict[str, Any]]:
        """
        Search memory entries by various criteria.

        Args:
            criteria: Search criteria (sender, intent, etc.)
            limit: Maximum number of results

        Returns:
            List of matching entries
        """
        results = []

        try:
            if "sender" in criteria:
                results.extend(memory.search_by_sender(criteria["sender"], limit))

            if "intent" in criteria:
                results.extend(memory.search_by_intent(criteria["intent"], limit))

            # Remove duplicates and limit results
            seen_ids = set()
            unique_results = []
            for entry in results:
                if entry.id not in seen_ids:
                    unique_results.append({
                        "id": entry.id,
                        "thread_id": entry.thread_id,
                        "source_type": entry.source_type,
                        "intent_type": entry.intent_type,
                        "sender": entry.sender,
                        "topic": entry.topic,
                        "timestamp": entry.timestamp.isoformat(),
                        "processing_agent": entry.processing_agent
                    })
                    seen_ids.add(entry.id)

                if len(unique_results) >= limit:
                    break

            return unique_results

        except Exception as e:
            self.logger.error(f"Search error: {e}")
            return []

    def _prepare_input(self, input_data: Union[str, Dict[str, Any]],
                      source_hint: Optional[str] = None) -> Dict[str, Any]:
        """
        Prepare input data for processing.

        Args:
            input_data: Raw input data
            source_hint: Optional source type hint

        Returns:
            Prepared input dictionary
        """
        if isinstance(input_data, str):
            # Handle string input (file path or raw content)
            if Path(input_data).exists():
                # It's a file path
                content, detected_format, metadata = file_handler.process_file(input_data)
                return {
                    "content": content,
                    "filename": Path(input_data).name,
                    "source_type": detected_format,
                    "file_metadata": metadata
                }
            else:
                # It's raw content
                content, detected_format, metadata = file_handler.process_raw_content(
                    input_data, source_hint or "text"
                )
                return {
                    "content": content,
                    "source_type": detected_format,
                    "content_metadata": metadata
                }

        elif isinstance(input_data, dict):
            # Already a dictionary, ensure required fields
            if "content" not in input_data:
                raise ValueError("Input dictionary must contain 'content' field")

            if "source_type" not in input_data and source_hint:
                input_data["source_type"] = source_hint

            return input_data

        else:
            raise ValueError(f"Unsupported input type: {type(input_data)}")

    def _route_to_agent(self, classification_result: Dict[str, Any],
                       prepared_input: Dict[str, Any], thread_id: str) -> Dict[str, Any]:
        """
        Route input to the appropriate specialized agent.

        Args:
            classification_result: Results from classifier
            prepared_input: Prepared input data
            thread_id: Thread ID

        Returns:
            Agent processing results
        """
        routing = classification_result["routing"]
        target_agent_name = routing["target_agent"]

        if target_agent_name not in self.agent_map:
            return {
                "success": False,
                "error": f"Unknown target agent: {target_agent_name}",
                "routing": routing
            }

        # Prepare input for specialized agent
        agent_input = {
            "content": prepared_input["content"],
            "intent_type": classification_result["classification"]["intent"],
            "classification": classification_result["classification"],
            "source_type": classification_result["classification"]["format"],
            "routing_info": routing
        }

        # Add any additional metadata
        if "filename" in prepared_input:
            agent_input["filename"] = prepared_input["filename"]

        # Process with specialized agent
        target_agent = self.agent_map[target_agent_name]
        return target_agent.process(agent_input, thread_id)

    def _combine_results(self, classification_result: Dict[str, Any],
                        routing_result: Dict[str, Any], thread_id: str) -> Dict[str, Any]:
        """
        Combine results from classification and specialized agent processing.

        Args:
            classification_result: Classifier results
            routing_result: Specialized agent results
            thread_id: Thread ID

        Returns:
            Combined final results
        """
        return {
            "success": routing_result.get("success", False),
            "thread_id": thread_id,
            "classification": classification_result.get("classification", {}),
            "routing": classification_result.get("routing", {}),
            "processing_results": routing_result,
            "memory_ids": {
                "classification": classification_result.get("memory_id"),
                "processing": routing_result.get("memory_id")
            },
            "metadata": {
                "total_processing_time": "N/A",  # Could add timing
                "agents_involved": [
                    "classifier",
                    classification_result.get("routing", {}).get("target_agent", "unknown")
                ]
            }
        }

# Global orchestrator instance
orchestrator = MultiAgentOrchestrator()

def main():
    """Main function for testing the system."""
    import sys

    if len(sys.argv) < 2:
        print("Usage: python main.py <file_path_or_content>")
        return

    input_arg = sys.argv[1]
    thread_id = sys.argv[2] if len(sys.argv) > 2 else None

    # Validate configuration
    if not config.validate_config():
        print("Warning: Configuration validation failed. Some features may not work.")

    # Process input
    result = orchestrator.process_input(input_arg, thread_id)

    # Print results
    import json
    print(json.dumps(result, indent=2, default=str))

if __name__ == "__main__":
    main()
