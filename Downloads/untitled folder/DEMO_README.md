# 🤖 Multi-Agent AI System - Interactive Demo

## 🚀 Quick Start

### Option 1: One-Click Launch
```bash
python run_demo.py
```

### Option 2: Manual Launch
```bash
# Install dependencies
pip install -r requirements.txt

# Run Streamlit app
streamlit run streamlit_app.py
```

## 🌐 Access the Demo

Once launched, open your browser to: **http://localhost:8501**

## 📱 Demo Features

### 🏠 Home Page
- **System Overview**: Architecture diagram and key features
- **Quick Stats**: Processing metrics and system status
- **Sample Files**: Available test files for demonstration

### 📝 Process Input
- **Upload Files**: Test with your own PDF, JSON, TXT, or EML files
- **Text Input**: Paste email content, JSON data, or any text
- **Sample Files**: Pre-loaded examples for quick testing
  - 📄 **Invoice (JSON)**: Structured invoice data processing
  - 📧 **Support Email**: Email analysis with urgency detection
  - 📋 **RFQ Request**: Request for Quote processing
  - ⚠️ **Customer Complaint**: Complaint handling and routing

### 📊 Analytics
- **Processing Timeline**: Visual timeline of system activity
- **Intent Distribution**: Pie chart of classified intents
- **Format Analysis**: Bar charts of input formats
- **Agent Usage**: Statistics on agent utilization

### 🔍 Memory Explorer
- **Thread Search**: Find conversations by thread ID
- **Sender Search**: Locate all communications from specific senders
- **Intent Filtering**: Filter by intent type (invoice, RFQ, etc.)
- **Complete History**: Browse all processed records

### ⚙️ System Status
- **Configuration**: Current system settings and API status
- **Metrics**: Real-time processing statistics
- **Maintenance**: Database cleanup tools

## 🎯 Demo Scenarios

### Scenario 1: Invoice Processing
1. Go to **Process Input** → **Sample Files**
2. Select "📄 Invoice (JSON)"
3. Click **🚀 Process Sample**
4. Observe:
   - Format detected as "JSON"
   - Intent classified as "Invoice"
   - Routing to JSON Agent
   - Data validation and extraction
   - Anomaly detection results

### Scenario 2: Email Analysis
1. Select "📧 Support Email" sample
2. Process and observe:
   - Format detected as "Email"
   - Intent classified as "Support" or "Complaint"
   - Urgency analysis (HIGH due to "URGENT" keywords)
   - Action items extracted
   - CRM-formatted output

### Scenario 3: Custom Input
1. Go to **Text Input**
2. Paste your own email or JSON content
3. Watch the system:
   - Automatically detect format
   - Classify intent
   - Route to appropriate agent
   - Store in memory with thread tracking

### Scenario 4: Analytics Exploration
1. Process several different inputs
2. Go to **Analytics** page
3. View:
   - Processing trends over time
   - Distribution of intents and formats
   - Agent usage patterns

## 🔧 Demo Configuration

### Works Without OpenAI API
- The demo includes fallback mechanisms
- Basic classification works without API key
- Full LLM features require OpenAI API key in `.env`

### Sample Data Included
- **4 sample files** covering different scenarios
- **Pre-configured schemas** for common document types
- **Realistic test data** for demonstration

### Memory Persistence
- All processing results stored in SQLite database
- Thread-based conversation tracking
- Historical analysis and search capabilities

## 📊 What You'll See

### Input Processing Flow
```
Your Input → Classifier Agent → Specialized Agent → Memory Storage → Results Display
```

### Real-Time Feedback
- ✅ **Success/Error indicators**
- 📊 **Confidence scores** for classifications
- 🎯 **Routing decisions** with explanations
- ⚡ **Urgency levels** for emails
- 🔍 **Anomaly detection** for data

### Detailed Output Logs
- **Classification Results**: Format, intent, confidence
- **Agent Processing**: Extracted data, validation, anomalies
- **Memory Storage**: Thread tracking, context preservation
- **Raw JSON Output**: Complete system response

## 🎨 Interactive Features

### Visual Elements
- 📈 **Charts and graphs** for analytics
- 🎯 **Progress indicators** during processing
- 🔄 **Real-time updates** of system metrics
- 🎨 **Color-coded** urgency and status indicators

### User Experience
- 📱 **Responsive design** works on desktop and mobile
- 🔍 **Search and filter** capabilities
- 📋 **Expandable sections** for detailed views
- 💾 **Persistent data** across sessions

## 🚨 Demo Limitations

### Without OpenAI API Key
- Uses rule-based fallback classification
- Limited natural language understanding
- Basic entity extraction only

### With OpenAI API Key
- Full LLM-powered classification
- Advanced content analysis
- Sophisticated entity extraction
- Better intent recognition

## 🔧 Troubleshooting

### Common Issues
1. **Port 8501 in use**: Change port in `run_demo.py`
2. **Dependencies missing**: Run `pip install -r requirements.txt`
3. **Database errors**: Delete `agent_memory.db` to reset

### Getting Help
- Check the **System Status** page for configuration issues
- View **Memory Explorer** to see if data is being stored
- Check console output for error messages

## 🎯 Next Steps

After exploring the demo:
1. **Set up OpenAI API key** for full functionality
2. **Customize agents** for your specific use cases
3. **Add new intent types** and processing logic
4. **Integrate with your existing systems**
5. **Scale with additional agents** and capabilities

## 📞 Support

For questions about the demo:
- Check the main `README.md` for detailed documentation
- Review the code in `streamlit_app.py` for implementation details
- Test with the provided sample files first
- Ensure all dependencies are properly installed

---

**🎉 Enjoy exploring the Multi-Agent AI System!**
