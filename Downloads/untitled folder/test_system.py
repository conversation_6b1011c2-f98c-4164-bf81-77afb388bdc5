#!/usr/bin/env python3
"""
Test script for the multi-agent AI system.
Demonstrates basic functionality without requiring OpenAI API.
"""

import json
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_basic_functionality():
    """Test basic system functionality."""
    print("🤖 Multi-Agent AI System Test")
    print("=" * 50)
    
    try:
        # Test configuration
        print("\n1. Testing Configuration...")
        from config import config
        print(f"   ✓ Supported formats: {config.FORMAT_TYPES}")
        print(f"   ✓ Intent types: {config.INTENT_TYPES}")
        print(f"   ✓ Max file size: {config.MAX_FILE_SIZE_MB}MB")
        
        # Test memory system
        print("\n2. Testing Memory System...")
        from shared_memory import memory, MemoryEntry
        
        # Create a test memory entry
        test_entry = MemoryEntry(
            thread_id="test-thread-001",
            source_type="json",
            intent_type="invoice",
            sender="<EMAIL>",
            topic="Test Invoice",
            extracted_fields={"amount": 100.0, "vendor": "Test Corp"},
            processing_agent="test_agent"
        )
        
        memory_id = memory.store_memory(test_entry)
        print(f"   ✓ Stored test memory entry: {memory_id}")
        
        # Retrieve the entry
        retrieved = memory.get_memory_by_id(memory_id)
        if retrieved:
            print(f"   ✓ Retrieved memory entry: {retrieved.sender}")
        
        # Test file handlers
        print("\n3. Testing File Handlers...")
        from utils.file_handlers import file_handler
        
        # Test JSON processing
        json_file = Path("examples/sample_invoice.json")
        if json_file.exists():
            content, format_type, metadata = file_handler.process_file(str(json_file))
            print(f"   ✓ Processed JSON file: format={format_type}, size={metadata['file_size_mb']:.2f}MB")
        
        # Test email processing
        email_file = Path("examples/sample_email.txt")
        if email_file.exists():
            content, format_type, metadata = file_handler.process_file(str(email_file))
            print(f"   ✓ Processed email file: format={format_type}, size={metadata['file_size_mb']:.2f}MB")
        
        # Test agents (without LLM)
        print("\n4. Testing Agents...")
        
        # Test classifier agent (fallback mode)
        from agents.classifier_agent import ClassifierAgent
        classifier = ClassifierAgent()
        
        test_input = {
            "content": '{"invoice_number": "INV-001", "amount": 500}',
            "source_type": "json"
        }
        
        result = classifier.process(test_input, "test-thread-002")
        print(f"   ✓ Classifier result: format={result.get('classification', {}).get('format')}")
        
        # Test JSON agent
        from agents.json_agent import JSONAgent
        json_agent = JSONAgent()
        
        json_input = {
            "content": {"invoice_number": "INV-001", "total_amount": 500.0},
            "intent_type": "invoice"
        }
        
        json_result = json_agent.process(json_input, "test-thread-003")
        print(f"   ✓ JSON agent processed: success={json_result.get('success')}")
        
        # Test email agent
        from agents.email_agent import EmailAgent
        email_agent = EmailAgent()
        
        email_input = {
            "content": "From: <EMAIL>\nSubject: Urgent Request\n\nThis is an urgent request for assistance.",
            "intent_type": "support"
        }
        
        email_result = email_agent.process(email_input, "test-thread-004")
        print(f"   ✓ Email agent processed: urgency={email_result.get('urgency_analysis', {}).get('level')}")
        
        print("\n5. Testing Orchestrator...")
        from main import orchestrator
        
        # Test with sample data
        sample_data = {
            "content": "From: <EMAIL>\nSubject: Invoice Question\n\nI have a question about invoice INV-123.",
            "source_type": "email"
        }
        
        orchestrator_result = orchestrator.process_input(sample_data, "test-thread-005")
        print(f"   ✓ Orchestrator result: success={orchestrator_result.get('success')}")
        
        # Test thread summary
        summary = orchestrator.get_thread_summary("test-thread-005")
        print(f"   ✓ Thread summary: {summary.get('total_entries', 0)} entries")
        
        print("\n✅ All tests completed successfully!")
        print("\n📋 System Status:")
        print(f"   • Memory entries created: 5+")
        print(f"   • Agents tested: 3 (Classifier, JSON, Email)")
        print(f"   • File formats supported: {len(config.FORMAT_TYPES)}")
        print(f"   • Intent types supported: {len(config.INTENT_TYPES)}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_example_files():
    """Test processing of example files."""
    print("\n🔍 Testing Example Files")
    print("=" * 30)
    
    example_files = [
        "examples/sample_invoice.json",
        "examples/sample_rfq.json", 
        "examples/sample_email.txt",
        "examples/sample_complaint.txt"
    ]
    
    from main import orchestrator
    
    for file_path in example_files:
        if Path(file_path).exists():
            try:
                print(f"\n📄 Processing: {file_path}")
                result = orchestrator.process_file(file_path)
                
                if result.get("success"):
                    classification = result.get("classification", {})
                    print(f"   ✓ Format: {classification.get('format')}")
                    print(f"   ✓ Intent: {classification.get('intent')}")
                    print(f"   ✓ Confidence: {classification.get('confidence', 'N/A')}")
                else:
                    print(f"   ❌ Processing failed: {result.get('error')}")
                    
            except Exception as e:
                print(f"   ❌ Error processing {file_path}: {e}")
        else:
            print(f"   ⚠️  File not found: {file_path}")

def show_system_info():
    """Display system information."""
    print("\n📊 System Information")
    print("=" * 25)
    
    try:
        from config import config
        from shared_memory import memory
        
        print(f"Database URL: {config.DATABASE_URL}")
        print(f"Log Level: {config.LOG_LEVEL}")
        print(f"OpenAI Model: {config.OPENAI_MODEL}")
        print(f"OpenAI API Key: {'✓ Set' if config.OPENAI_API_KEY else '❌ Not Set'}")
        
        # Check memory database
        try:
            with memory.get_session() as session:
                from shared_memory import MemoryRecord
                count = session.query(MemoryRecord).count()
                print(f"Memory entries: {count}")
        except Exception as e:
            print(f"Memory database: ❌ Error ({e})")
            
    except Exception as e:
        print(f"Error getting system info: {e}")

def main():
    """Main test function."""
    print("🚀 Multi-Agent AI System - Test Suite")
    print("=" * 60)
    
    # Show system info
    show_system_info()
    
    # Run basic functionality tests
    if test_basic_functionality():
        # Test example files
        test_example_files()
        
        print("\n🎉 Test suite completed!")
        print("\n💡 Next steps:")
        print("   1. Set up OpenAI API key in .env file for full functionality")
        print("   2. Try: python main.py examples/sample_invoice.json")
        print("   3. Explore the examples/ directory for sample inputs")
        print("   4. Check agent_memory.db for stored processing results")
    else:
        print("\n❌ Basic tests failed. Please check the error messages above.")
        return 1
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
