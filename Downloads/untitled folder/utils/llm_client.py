"""LLM client wrapper for OpenAI integration."""

import json
import logging
from typing import Dict, List, Optional, Any
from openai import OpenAI

from config import config

class LLMClient:
    """Wrapper for OpenAI API calls."""

    def __init__(self):
        # For demo mode, disable OpenAI to use fallback mechanisms
        api_key = config.OPENAI_API_KEY
        if api_key and api_key != "demo_mode" and len(api_key) > 10:
            self.client = OpenAI(api_key=api_key)
        else:
            self.client = None

        self.model = config.OPENAI_MODEL
        self.logger = logging.getLogger("llm_client")

        if not self.client:
            self.logger.info("Running in demo mode - using fallback classification")

    def classify_format_and_intent(self, content: str, filename: Optional[str] = None) -> Dict[str, str]:
        """
        Classify the format and intent of input content.

        Args:
            content: The content to classify
            filename: Optional filename for additional context

        Returns:
            Dictionary with 'format' and 'intent' keys
        """
        if not self.client:
            return self._fallback_classification(content, filename)

        prompt = self._build_classification_prompt(content, filename)

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a document classifier. Respond only with valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=150
            )

            result = json.loads(response.choices[0].message.content)

            # Validate and normalize results
            format_type = result.get("format", "text").lower()
            intent_type = result.get("intent", "other").lower()

            # Ensure format is in supported list
            if format_type not in config.FORMAT_TYPES:
                format_type = "text"

            # Ensure intent is in supported list
            if intent_type not in config.INTENT_TYPES:
                intent_type = "other"

            return {
                "format": format_type,
                "intent": intent_type,
                "confidence": result.get("confidence", 0.5)
            }

        except Exception as e:
            self.logger.error(f"LLM classification failed: {e}")
            return self._fallback_classification(content, filename)

    def extract_structured_data(self, content: str, schema: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract structured data from content based on a schema.

        Args:
            content: Content to extract from
            schema: Target schema for extraction

        Returns:
            Extracted structured data
        """
        if not self.client:
            return self._fallback_extraction(content, schema)

        prompt = f"""
        Extract the following information from the content below and return as JSON:

        Schema: {json.dumps(schema, indent=2)}

        Content:
        {content[:2000]}  # Limit content length

        Return only valid JSON with the requested fields. If a field cannot be found, use null.
        """

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a data extraction specialist. Return only valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=500
            )

            return json.loads(response.choices[0].message.content)

        except Exception as e:
            self.logger.error(f"LLM extraction failed: {e}")
            return self._fallback_extraction(content, schema)

    def analyze_email_content(self, content: str) -> Dict[str, Any]:
        """
        Analyze email content for sender, urgency, and intent.

        Args:
            content: Email content to analyze

        Returns:
            Analysis results
        """
        if not self.client:
            return self._fallback_email_analysis(content)

        prompt = f"""
        Analyze this email content and extract the following information as JSON:

        {{
            "sender": "sender email or name",
            "subject": "email subject",
            "urgency": "low|medium|high",
            "intent": "one of: {', '.join(config.INTENT_TYPES)}",
            "key_points": ["list", "of", "key", "points"],
            "action_required": true/false,
            "deadline": "any mentioned deadline or null"
        }}

        Email content:
        {content[:2000]}
        """

        try:
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are an email analysis specialist. Return only valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,
                max_tokens=400
            )

            return json.loads(response.choices[0].message.content)

        except Exception as e:
            self.logger.error(f"Email analysis failed: {e}")
            return self._fallback_email_analysis(content)

    def _build_classification_prompt(self, content: str, filename: Optional[str] = None) -> str:
        """Build prompt for format and intent classification."""
        file_context = f"Filename: {filename}\n" if filename else ""

        return f"""
        {file_context}Classify this content and return JSON with format and intent:

        {{
            "format": "one of: {', '.join(config.FORMAT_TYPES)}",
            "intent": "one of: {', '.join(config.INTENT_TYPES)}",
            "confidence": 0.0-1.0
        }}

        Content (first 1000 chars):
        {content[:1000]}
        """

    def _fallback_classification(self, content: str, filename: Optional[str] = None) -> Dict[str, str]:
        """Fallback classification when LLM is unavailable."""
        # Simple heuristic-based classification
        content_lower = content.lower()

        # Format detection
        if filename:
            if filename.endswith('.pdf'):
                format_type = "pdf"
            elif filename.endswith('.json'):
                format_type = "json"
            elif filename.endswith(('.eml', '.msg')):
                format_type = "email"
            else:
                format_type = "text"
        else:
            # Check for JSON structure
            if content.strip().startswith('{') and content.strip().endswith('}'):
                try:
                    json.loads(content)
                    format_type = "json"
                except json.JSONDecodeError:
                    format_type = "text"
            # Check for email format (relaxed check)
            elif any(header in content_lower for header in ["from:", "subject:"]) and "@" in content:
                format_type = "email"
            # Check for PDF-like content
            elif any(indicator in content_lower for indicator in ["page", "chapter", "section", "figure", "table"]):
                format_type = "pdf"
            else:
                format_type = "text"

        # Intent detection (improved keywords)
        if any(word in content_lower for word in ["invoice", "bill", "payment", "invoice_number", "total_amount"]):
            intent_type = "invoice"
        elif any(word in content_lower for word in ["rfq", "quote", "proposal", "request for quote", "rfq_number"]):
            intent_type = "rfq"
        elif any(word in content_lower for word in ["complaint", "issue", "problem", "dissatisfied", "unhappy", "angry", "disappointed"]):
            intent_type = "complaint"
        elif any(word in content_lower for word in ["urgent", "emergency", "asap", "production", "down", "critical"]):
            intent_type = "support"
        elif "order" in content_lower:
            intent_type = "order"
        else:
            intent_type = "inquiry"

        # Boost confidence for clear indicators
        confidence = 0.8 if format_type in ["json", "email"] else 0.6

        return {
            "format": format_type,
            "intent": intent_type,
            "confidence": confidence
        }

    def _fallback_extraction(self, content: str, schema: Dict[str, Any]) -> Dict[str, Any]:
        """Fallback extraction when LLM is unavailable."""
        # Return schema with null values
        result = {}
        for key in schema.keys():
            result[key] = None
        return result

    def _fallback_email_analysis(self, content: str) -> Dict[str, Any]:
        """Fallback email analysis when LLM is unavailable."""
        return {
            "sender": None,
            "subject": None,
            "urgency": "medium",
            "intent": "other",
            "key_points": [],
            "action_required": False,
            "deadline": None
        }

# Global LLM client instance
llm_client = LLMClient()
