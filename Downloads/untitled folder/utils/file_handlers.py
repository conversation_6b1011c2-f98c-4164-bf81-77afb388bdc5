"""File handling utilities for different input formats."""

import json
import logging
from typing import Dict, <PERSON>, <PERSON><PERSON>, <PERSON>ple
from pathlib import Path
import PyPDF2
import email
from email import policy
from io import String<PERSON>

from config import config

class FileHandler:
    """Handles different file formats and extracts content."""
    
    def __init__(self):
        self.logger = logging.getLogger("file_handler")
    
    def process_file(self, file_path: str) -> Tuple[str, str, Dict[str, Any]]:
        """
        Process a file and extract content.
        
        Args:
            file_path: Path to the file to process
            
        Returns:
            Tuple of (content, detected_format, metadata)
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
        
        # Check file size
        file_size_mb = file_path.stat().st_size / (1024 * 1024)
        if file_size_mb > config.MAX_FILE_SIZE_MB:
            raise ValueError(f"File too large: {file_size_mb:.1f}MB (max: {config.MAX_FILE_SIZE_MB}MB)")
        
        # Determine file type and process accordingly
        file_extension = file_path.suffix.lower()
        
        metadata = {
            "filename": file_path.name,
            "file_size_mb": file_size_mb,
            "file_extension": file_extension
        }
        
        if file_extension == '.pdf':
            content, format_type = self._process_pdf(file_path)
        elif file_extension == '.json':
            content, format_type = self._process_json(file_path)
        elif file_extension in ['.eml', '.msg']:
            content, format_type = self._process_email_file(file_path)
        elif file_extension in ['.txt', '.text']:
            content, format_type = self._process_text(file_path)
        else:
            # Try to process as text
            content, format_type = self._process_text(file_path)
        
        return content, format_type, metadata
    
    def process_raw_content(self, content: str, content_type: str = "text") -> Tuple[str, str, Dict[str, Any]]:
        """
        Process raw content string.
        
        Args:
            content: Raw content string
            content_type: Hint about content type
            
        Returns:
            Tuple of (content, detected_format, metadata)
        """
        metadata = {
            "content_length": len(content),
            "content_type_hint": content_type
        }
        
        # Try to detect format from content
        if content_type.lower() == "json" or (content.strip().startswith('{') and content.strip().endswith('}')):
            try:
                # Validate JSON
                json.loads(content)
                return content, "json", metadata
            except json.JSONDecodeError:
                pass
        
        # Check if it looks like an email
        if self._looks_like_email(content):
            return content, "email", metadata
        
        # Default to text
        return content, "text", metadata
    
    def _process_pdf(self, file_path: Path) -> Tuple[str, str]:
        """Extract text from PDF file."""
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                text_content = []
                
                for page in pdf_reader.pages:
                    text_content.append(page.extract_text())
                
                content = '\n'.join(text_content)
                return content, "pdf"
                
        except Exception as e:
            self.logger.error(f"Error processing PDF {file_path}: {e}")
            raise ValueError(f"Could not process PDF file: {e}")
    
    def _process_json(self, file_path: Path) -> Tuple[str, str]:
        """Process JSON file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
                # Validate JSON
                json.loads(content)
                return content, "json"
                
        except Exception as e:
            self.logger.error(f"Error processing JSON file {file_path}: {e}")
            raise ValueError(f"Could not process JSON file: {e}")
    
    def _process_email_file(self, file_path: Path) -> Tuple[str, str]:
        """Process email file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                msg = email.message_from_file(file, policy=policy.default)
                
                # Extract email components
                email_content = []
                email_content.append(f"From: {msg.get('From', 'Unknown')}")
                email_content.append(f"To: {msg.get('To', 'Unknown')}")
                email_content.append(f"Subject: {msg.get('Subject', 'No Subject')}")
                email_content.append(f"Date: {msg.get('Date', 'Unknown')}")
                email_content.append("")  # Empty line
                
                # Get body content
                if msg.is_multipart():
                    for part in msg.walk():
                        if part.get_content_type() == "text/plain":
                            email_content.append(part.get_content())
                            break
                else:
                    email_content.append(msg.get_content())
                
                content = '\n'.join(email_content)
                return content, "email"
                
        except Exception as e:
            self.logger.error(f"Error processing email file {file_path}: {e}")
            raise ValueError(f"Could not process email file: {e}")
    
    def _process_text(self, file_path: Path) -> Tuple[str, str]:
        """Process plain text file."""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
                
                # Check if it looks like an email
                if self._looks_like_email(content):
                    return content, "email"
                
                return content, "text"
                
        except UnicodeDecodeError:
            # Try with different encoding
            try:
                with open(file_path, 'r', encoding='latin-1') as file:
                    content = file.read()
                    return content, "text"
            except Exception as e:
                self.logger.error(f"Error processing text file {file_path}: {e}")
                raise ValueError(f"Could not process text file: {e}")
        except Exception as e:
            self.logger.error(f"Error processing text file {file_path}: {e}")
            raise ValueError(f"Could not process text file: {e}")
    
    def _looks_like_email(self, content: str) -> bool:
        """Check if content looks like an email."""
        content_lower = content.lower()
        
        # Must have all of these basic email headers
        required_headers = [
            "from:",
            "to:",
            "subject:",
            "date:"
        ]
        
        # Check if all required headers are present
        has_all_headers = all(header in content_lower for header in required_headers)
        
        # Check for email pattern in From/To fields
        has_email_pattern = False
        for line in content.split('\n'):
            line_lower = line.lower()
            if line_lower.startswith('from:') or line_lower.startswith('to:'):
                if '@' in line and '.' in line:
                    has_email_pattern = True
                    break
        
        # Must have both all headers and email pattern
        return has_all_headers and has_email_pattern

# Global file handler instance
file_handler = FileHandler()
