"""Configuration settings for the multi-agent AI system."""

import os
from typing import List, Dict
from dotenv import load_dotenv

load_dotenv()

class Config:
    """Configuration class for the multi-agent system."""
    
    # OpenAI Configuration
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    OPENAI_MODEL = os.getenv("OPENAI_MODEL", "gpt-3.5-turbo")
    
    # Database Configuration
    DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///agent_memory.db")
    
    # File Processing Configuration
    MAX_FILE_SIZE_MB = int(os.getenv("MAX_FILE_SIZE_MB", "10"))
    SUPPORTED_FORMATS = ["pdf", "json", "txt", "eml"]
    
    # Intent Classifications
    INTENT_TYPES = [
        "invoice",
        "rfq",  # Request for Quote
        "complaint",
        "regulation",
        "inquiry",
        "order",
        "support",
        "other"
    ]
    
    # Format Classifications
    FORMAT_TYPES = [
        "pdf",
        "json", 
        "email",
        "text"
    ]
    
    # Agent Configuration
    AGENT_TIMEOUT_SECONDS = 30
    MAX_RETRIES = 3
    
    # Logging Configuration
    LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")
    LOG_FILE = os.getenv("LOG_FILE", "agent_system.log")
    
    # Memory Configuration
    MEMORY_RETENTION_DAYS = int(os.getenv("MEMORY_RETENTION_DAYS", "30"))
    
    @classmethod
    def validate_config(cls) -> bool:
        """Validate that required configuration is present."""
        if not cls.OPENAI_API_KEY:
            print("Warning: OPENAI_API_KEY not set. Some features may not work.")
            return False
        return True

# Global configuration instance
config = Config()
