#!/usr/bin/env python3
"""
Startup script for the Multi-Agent AI System Streamlit Demo
Handles setup, dependency checking, and launches the web interface.
"""

import subprocess
import sys
import os
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are installed."""
    required_packages = [
        'streamlit',
        'plotly', 
        'pandas',
        'sqlalchemy',
        'pydantic'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    return missing_packages

def install_dependencies():
    """Install missing dependencies."""
    print("📦 Installing dependencies...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'])
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False

def setup_environment():
    """Set up the environment for the demo."""
    print("🔧 Setting up environment...")
    
    # Create .env file if it doesn't exist
    env_file = Path('.env')
    if not env_file.exists():
        env_template = Path('.env.template')
        if env_template.exists():
            print("📝 Creating .env file from template...")
            with open(env_template, 'r') as template:
                content = template.read()
            
            # Set demo-friendly defaults
            content = content.replace('your_openai_api_key_here', 'demo_mode')
            content += '\n# Demo mode - OpenAI features will use fallback\n'
            
            with open(env_file, 'w') as env:
                env.write(content)
            
            print("✅ Environment file created!")
        else:
            print("⚠️  No .env.template found, creating basic .env...")
            with open(env_file, 'w') as env:
                env.write("""# Multi-Agent AI System Demo Configuration
OPENAI_API_KEY=demo_mode
OPENAI_MODEL=gpt-3.5-turbo
DATABASE_URL=sqlite:///agent_memory.db
LOG_LEVEL=INFO
""")
    
    # Initialize database
    print("🗄️  Initializing database...")
    try:
        from shared_memory import memory
        # This will create the database tables
        print("✅ Database initialized!")
    except Exception as e:
        print(f"⚠️  Database initialization warning: {e}")
    
    return True

def run_streamlit():
    """Launch the Streamlit application."""
    print("🚀 Launching Streamlit demo...")
    print("📱 The web interface will open in your browser")
    print("🔗 URL: http://localhost:8501")
    print("\n" + "="*50)
    print("🤖 MULTI-AGENT AI SYSTEM DEMO")
    print("="*50)
    
    try:
        # Run streamlit
        subprocess.run([
            sys.executable, '-m', 'streamlit', 'run', 'streamlit_app.py',
            '--server.port', '8501',
            '--server.address', 'localhost',
            '--browser.gatherUsageStats', 'false'
        ])
    except KeyboardInterrupt:
        print("\n👋 Demo stopped by user")
    except Exception as e:
        print(f"❌ Error running Streamlit: {e}")

def main():
    """Main function to set up and run the demo."""
    print("🤖 Multi-Agent AI System - Streamlit Demo Setup")
    print("=" * 60)
    
    # Change to script directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # Check dependencies
    print("🔍 Checking dependencies...")
    missing = check_dependencies()
    
    if missing:
        print(f"❌ Missing packages: {', '.join(missing)}")
        print("📦 Installing missing dependencies...")
        
        if not install_dependencies():
            print("❌ Failed to install dependencies. Please run:")
            print("   pip install -r requirements.txt")
            return 1
    else:
        print("✅ All dependencies are installed!")
    
    # Set up environment
    if not setup_environment():
        print("❌ Environment setup failed!")
        return 1
    
    # Show demo information
    print("\n" + "="*60)
    print("🎯 DEMO FEATURES:")
    print("• 📝 Process various input formats (JSON, Email, Text)")
    print("• 🤖 See agent classification and routing in action")
    print("• 📊 View analytics and processing statistics")
    print("• 🔍 Explore memory and conversation history")
    print("• ⚙️  Monitor system status and configuration")
    print("="*60)
    
    print("\n💡 DEMO TIPS:")
    print("• Use the sample files provided for quick testing")
    print("• Try different input types to see agent routing")
    print("• Check the Analytics page for visualizations")
    print("• Explore Memory to see conversation tracking")
    print("• System works in demo mode without OpenAI API key")
    
    print("\n🚀 Starting demo in 3 seconds...")
    import time
    time.sleep(3)
    
    # Run the demo
    run_streamlit()
    
    return 0

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
