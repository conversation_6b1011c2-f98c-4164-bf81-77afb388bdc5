"""
Streamlit Web Application for Multi-Agent AI System Demo
Interactive interface to test and demonstrate the system functionality.
"""

import streamlit as st
import json
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime, timedelta
import uuid
from pathlib import Path
import sys

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

# Import our system components
from main import orchestrator
from shared_memory import memory
from config import config

# Page configuration
st.set_page_config(
    page_title="Multi-Agent AI System Demo",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .agent-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        margin: 0.5rem 0;
    }
    .success-box {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
        padding: 1rem;
        border-radius: 0.25rem;
        margin: 1rem 0;
    }
    .error-box {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
        padding: 1rem;
        border-radius: 0.25rem;
        margin: 1rem 0;
    }
    .info-box {
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        color: #0c5460;
        padding: 1rem;
        border-radius: 0.25rem;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)

def main():
    """Main Streamlit application."""

    # Header
    st.markdown('<h1 class="main-header"> Multi-Agent AI System Demo</h1>', unsafe_allow_html=True)

    # Sidebar navigation
    st.sidebar.title("Navigation")
    page = st.sidebar.selectbox(
        "Choose a page:",
        ["Home", "Process Input", "Analytics", " Memory Explorer", "⚙️ System Status"]
    )

    if page == "Home":
        show_home_page()
    elif page == "Process Input":
        show_process_page()
    elif page == "Analytics":
        show_analytics_page()
    elif page == "Memory Explorer":
        show_memory_page()
    elif page == "⚙️ System Status":
        show_status_page()

def show_home_page():
    """Display the home page with system overview."""

    st.markdown("## Welcome to the Multi-Agent AI System")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("""
        ### System Overview
        This intelligent system processes various input formats and routes them to specialized agents:

        - **Format Detection**: PDF, JSON, Email, Text
        - **Intent Classification**: Invoice, RFQ, Complaint, etc.
        - **Specialized Agents**: JSON, Email processing
        - **Shared Memory**: Context and traceability
        """)

        # System architecture diagram
        st.markdown("### Architecture")
        st.code("""
        Input → Classifier Agent → Format & Intent Detection
                      ↓
        JSON Agent ←  Router  → Email Agent
                      ↓
                Shared Memory
                      ↓
                   Results
        """, language="text")

    with col2:
        st.markdown("### Quick Stats")

        # Get system statistics
        try:
            with memory.get_session() as session:
                from shared_memory import MemoryRecord
                total_entries = session.query(MemoryRecord).count()
                recent_entries = session.query(MemoryRecord).filter(
                    MemoryRecord.timestamp >= datetime.utcnow() - timedelta(days=7)
                ).count()
        except:
            total_entries = 0
            recent_entries = 0

        # Display metrics
        col2_1, col2_2 = st.columns(2)
        with col2_1:
            st.metric("Total Processed", total_entries)
            st.metric("Supported Formats", len(config.FORMAT_TYPES))
        with col2_2:
            st.metric("This Week", recent_entries)
            st.metric("Intent Types", len(config.INTENT_TYPES))

        # Sample files section
        st.markdown("### Sample Files Available")
        sample_files = [
            "sample_invoice.json - Invoice processing",
            "sample_email.txt - Email analysis",
            "sample_rfq.json - RFQ processing",
            "⚠️ sample_complaint.txt - Complaint handling"
        ]
        for file in sample_files:
            st.markdown(f"- {file}")

def show_process_page():
    """Display the input processing page."""

    st.markdown("## Process Input")

    # Input method selection
    input_method = st.radio(
        "Choose input method:",
        ["Upload File", "Text Input", "Sample Files"]
    )

    thread_id = st.text_input("Thread ID (optional)", placeholder="Leave empty for auto-generation")
    if not thread_id:
        thread_id = str(uuid.uuid4())[:8]

    result = None

    if input_method == "Upload File":
        uploaded_file = st.file_uploader(
            "Choose a file",
            type=['json', 'txt', 'pdf', 'eml'],
            help="Supported formats: JSON, TXT, PDF, EML"
        )

        if uploaded_file is not None:
            if st.button("Process File"):
                with st.spinner("Processing file..."):
                    # Save uploaded file temporarily
                    temp_path = f"temp_{uploaded_file.name}"
                    with open(temp_path, "wb") as f:
                        f.write(uploaded_file.getbuffer())

                    # Process the file
                    result = orchestrator.process_file(temp_path, thread_id)

                    # Clean up
                    Path(temp_path).unlink(missing_ok=True)

    elif input_method == "Text Input":
        content = st.text_area(
            "Enter your content:",
            height=200,
            placeholder="Paste email content, JSON data, or any text here..."
        )

        source_hint = st.selectbox(
            "Content type hint:",
            ["auto", "email", "json", "text"]
        )

        if content and st.button("🚀 Process Text"):
            with st.spinner("Processing content..."):
                input_data = {
                    "content": content,
                    "source_type": source_hint if source_hint != "auto" else None
                }
                result = orchestrator.process_input(input_data, thread_id)

    elif input_method == "Sample Files":
        sample_files = {
            "Invoice (JSON)": "examples/sample_invoice.json",
            "Support Email": "examples/sample_email.txt",
            "RFQ Request": "examples/sample_rfq.json",
            "⚠️ Customer Complaint": "examples/sample_complaint.txt"
        }

        selected_sample = st.selectbox("Choose a sample file:", list(sample_files.keys()))

        # Show preview of selected file
        if selected_sample:
            file_path = sample_files[selected_sample]
            if Path(file_path).exists():
                with open(file_path, 'r') as f:
                    content = f.read()

                st.markdown("### File Preview")
                st.code(content[:500] + "..." if len(content) > 500 else content)

                if st.button("🚀 Process Sample"):
                    with st.spinner("Processing sample file..."):
                        result = orchestrator.process_file(file_path, thread_id)

    # Display results
    if result:
        display_processing_results(result, thread_id)

def display_processing_results(result, thread_id):
    """Display the processing results in a structured format."""

    st.markdown("## 📊 Processing Results")

    # Success/Error indicator
    if result.get("success"):
        st.markdown('<div class="success-box">✅ Processing completed successfully!</div>', unsafe_allow_html=True)
    else:
        error_msg = result.get("error", "Unknown error")
        st.markdown(f'<div class="error-box">❌ Processing failed: {error_msg}</div>', unsafe_allow_html=True)
        
        # Show additional error details if available
        if "stage" in result:
            st.markdown(f"**Error Stage:** {result['stage']}")
        if "input_summary" in result:
            st.markdown("**Input Summary:**")
            st.json(result["input_summary"])
        return

    # Create tabs for different result sections
    tab1, tab2, tab3, tab4 = st.tabs(["🎯 Classification", "🤖 Agent Results", "🧠 Memory", "📋 Raw Output"])

    with tab1:
        st.markdown("### Classification Results")
        classification = result.get("classification", {})

        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Format", classification.get("format", "Unknown"))
        with col2:
            st.metric("Intent", classification.get("intent", "Unknown"))
        with col3:
            confidence = classification.get("confidence", 0)
            st.metric("Confidence", f"{confidence:.2f}" if isinstance(confidence, (int, float)) else str(confidence))

        # Routing information
        routing = result.get("routing", {})
        if routing:
            st.markdown("### Routing Decision")
            st.json(routing)

    with tab2:
        st.markdown("### Agent Processing Results")
        processing_results = result.get("processing_results", {})

        if processing_results:
            # Show different results based on agent type
            if "email_metadata" in processing_results:
                show_email_results(processing_results)
            elif "extracted_data" in processing_results:
                show_json_results(processing_results)
            else:
                st.json(processing_results)

    with tab3:
        st.markdown("### Memory Storage")
        memory_ids = result.get("memory_ids", {})

        col1, col2 = st.columns(2)
        with col1:
            st.markdown("**Classification Memory ID:**")
            st.code(memory_ids.get("classification", "N/A"))
        with col2:
            st.markdown("**Processing Memory ID:**")
            st.code(memory_ids.get("processing", "N/A"))

        st.markdown(f"**Thread ID:** `{thread_id}`")

        # Show thread history
        if st.button("📜 View Thread History"):
            show_thread_history(thread_id)

    with tab4:
        st.markdown("### Complete Raw Output")
        st.json(result)

def show_email_results(results):
    """Display email processing results."""

    # Email metadata
    email_metadata = results.get("email_metadata", {})
    if email_metadata:
        st.markdown("#### Email Metadata")
        col1, col2 = st.columns(2)
        with col1:
            st.markdown(f"**Sender:** {email_metadata.get('sender', 'Unknown')}")
            st.markdown(f"**Subject:** {email_metadata.get('subject', 'No subject')}")
        with col2:
            st.markdown(f"**Date:** {email_metadata.get('date', 'Unknown')}")
            st.markdown(f"**Recipient:** {email_metadata.get('recipient', 'Unknown')}")

    # Urgency analysis
    urgency = results.get("urgency_analysis", {})
    if urgency:
        st.markdown("#### Urgency Analysis")
        urgency_level = urgency.get("level", "unknown")
        urgency_color = {"high": "🔴", "medium": "🟡", "low": "🟢"}.get(urgency_level, "⚪")
        st.markdown(f"**Level:** {urgency_color} {urgency_level.upper()}")

        matched_keywords = urgency.get("matched_keywords", [])
        if matched_keywords:
            st.markdown("**Matched Keywords:**")
            for keyword, level in matched_keywords:
                st.markdown(f"- {keyword} ({level})")

    # Action items
    action_items = results.get("action_items", [])
    if action_items:
        st.markdown("#### Action Items")
        for i, item in enumerate(action_items, 1):
            priority_icon = {"high": "🔴", "medium": "🟡", "low": "🟢"}.get(item.get("priority", "medium"), "⚪")
            st.markdown(f"{i}. {priority_icon} {item.get('action', 'Unknown action')} ({item.get('type', 'unknown')})")

    # CRM formatted data
    crm_data = results.get("crm_formatted", {})
    if crm_data:
        st.markdown("#### CRM Formatted Data")
        st.json(crm_data)

def show_json_results(results):
    """Display JSON processing results."""

    # Extracted data
    extracted_data = results.get("extracted_data", {})
    if extracted_data:
        st.markdown("#### Extracted Data")
        st.json(extracted_data)

    # Validation results
    validation = results.get("validation_results", {})
    if validation:
        st.markdown("#### Validation Results")
        is_valid = validation.get("is_valid", False)
        status_icon = "✅" if is_valid else "❌"
        st.markdown(f"**Status:** {status_icon} {'Valid' if is_valid else 'Invalid'}")

        errors = validation.get("errors", [])
        if errors:
            st.markdown("**Errors:**")
            for error in errors:
                st.markdown(f"- ❌ {error}")

    # Anomalies
    anomalies = results.get("anomalies", [])
    if anomalies:
        st.markdown("#### ⚠️ Detected Anomalies")
        for anomaly in anomalies:
            severity_icon = {"high": "🔴", "medium": "🟡", "low": "🟢"}.get(anomaly.get("severity", "medium"), "⚪")
            st.markdown(f"- {severity_icon} **{anomaly.get('type', 'Unknown')}**: {anomaly.get('message', 'No message')}")

def show_thread_history(thread_id):
    """Display thread history."""

    try:
        history = memory.get_thread_history(thread_id, limit=20)

        if not history:
            st.info("No history found for this thread.")
            return

        st.markdown(f"### 📜 Thread History ({len(history)} entries)")

        # Create a timeline visualization
        timeline_data = []
        for entry in history:
            timeline_data.append({
                "timestamp": entry.timestamp,
                "agent": entry.processing_agent,
                "source_type": entry.source_type,
                "intent_type": entry.intent_type,
                "sender": entry.sender or "Unknown"
            })

        df = pd.DataFrame(timeline_data)

        # Display as a table
        st.dataframe(df, use_container_width=True)

        # Show detailed entries
        for i, entry in enumerate(history):
            with st.expander(f"Entry {i+1}: {entry.processing_agent} - {entry.intent_type}"):
                col1, col2 = st.columns(2)
                with col1:
                    st.markdown(f"**Agent:** {entry.processing_agent}")
                    st.markdown(f"**Source:** {entry.source_type}")
                    st.markdown(f"**Intent:** {entry.intent_type}")
                with col2:
                    st.markdown(f"**Sender:** {entry.sender or 'N/A'}")
                    st.markdown(f"**Topic:** {entry.topic or 'N/A'}")
                    st.markdown(f"**Timestamp:** {entry.timestamp}")

                if entry.extracted_fields:
                    st.markdown("**Extracted Fields:**")
                    st.json(entry.extracted_fields)

    except Exception as e:
        st.error(f"Error loading thread history: {e}")

def show_analytics_page():
    """Display analytics and statistics."""

    st.markdown("## System Analytics")

    try:
        with memory.get_session() as session:
            from shared_memory import MemoryRecord

            # Get all records
            records = session.query(MemoryRecord).all()

            if not records:
                st.info("No data available for analytics. Process some inputs first!")
                return

            # Convert to DataFrame
            data = []
            for record in records:
                data.append({
                    "timestamp": record.timestamp,
                    "agent": record.processing_agent,
                    "source_type": record.source_type,
                    "intent_type": record.intent_type,
                    "sender": record.sender,
                    "thread_id": record.thread_id
                })

            df = pd.DataFrame(data)

            # Time series analysis
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("### Processing Over Time")
                df['date'] = df['timestamp'].dt.date
                daily_counts = df.groupby('date').size().reset_index(name='count')

                fig = px.line(daily_counts, x='date', y='count', title="Daily Processing Volume")
                st.plotly_chart(fig, use_container_width=True)

            with col2:
                st.markdown("### Intent Distribution")
                intent_counts = df['intent_type'].value_counts()

                fig = px.pie(values=intent_counts.values, names=intent_counts.index, title="Intent Types")
                st.plotly_chart(fig, use_container_width=True)

            # Format and agent analysis
            col3, col4 = st.columns(2)

            with col3:
                st.markdown("### Format Distribution")
                format_counts = df['source_type'].value_counts()

                fig = px.bar(x=format_counts.index, y=format_counts.values, title="Input Formats")
                st.plotly_chart(fig, use_container_width=True)

            with col4:
                st.markdown("### Agent Usage")
                agent_counts = df['agent'].value_counts()

                fig = px.bar(x=agent_counts.index, y=agent_counts.values, title="Agent Processing Count")
                st.plotly_chart(fig, use_container_width=True)

            # Summary statistics
            st.markdown("### Summary Statistics")
            col5, col6, col7, col8 = st.columns(4)

            with col5:
                st.metric("Total Processed", len(df))
            with col6:
                st.metric("Unique Threads", df['thread_id'].nunique())
            with col7:
                st.metric("Unique Senders", df['sender'].nunique())
            with col8:
                st.metric("Most Common Intent", df['intent_type'].mode().iloc[0] if not df.empty else "N/A")

    except Exception as e:
        st.error(f"Error loading analytics: {e}")

def show_memory_page():
    """Display memory explorer."""

    st.markdown("## Memory Explorer")

    # Search functionality
    search_type = st.selectbox("Search by:", ["Thread ID", "Sender", "Intent Type", "All Records"])

    if search_type == "Thread ID":
        thread_id = st.text_input("Enter Thread ID:")
        if thread_id and st.button("🔍 Search"):
            show_thread_history(thread_id)

    elif search_type == "Sender":
        sender = st.text_input("Enter Sender:")
        if sender and st.button("Search"):
            try:
                results = memory.search_by_sender(sender, limit=20)
                display_search_results(results)
            except Exception as e:
                st.error(f"Search error: {e}")

    elif search_type == "Intent Type":
        intent = st.selectbox("Select Intent:", config.INTENT_TYPES)
        if st.button("Search"):
            try:
                results = memory.search_by_intent(intent, limit=20)
                display_search_results(results)
            except Exception as e:
                st.error(f"Search error: {e}")

    elif search_type == "All Records":
        if st.button("Show All Records"):
            try:
                with memory.get_session() as session:
                    from shared_memory import MemoryRecord
                    records = session.query(MemoryRecord).order_by(MemoryRecord.timestamp.desc()).limit(50).all()

                    if records:
                        data = []
                        for record in records:
                            data.append({
                                "ID": record.id,
                                "Thread": record.thread_id,
                                "Agent": record.processing_agent,
                                "Source": record.source_type,
                                "Intent": record.intent_type,
                                "Sender": record.sender or "N/A",
                                "Topic": record.topic or "N/A",
                                "Timestamp": record.timestamp.strftime("%Y-%m-%d %H:%M"),
                                "Extracted Fields": record.extracted_fields
                            })

                        df = pd.DataFrame(data)
                        st.dataframe(df, use_container_width=True)
                        
                        # Show detailed view for selected record
                        if len(records) > 0:
                            st.markdown("### Record Details")
                            selected_id = st.selectbox("Select a record to view details:", df["ID"])
                            if selected_id:
                                record = next((r for r in records if r.id == selected_id), None)
                                if record:
                                    st.json({
                                        "ID": record.id,
                                        "Thread ID": record.thread_id,
                                        "Processing Agent": record.processing_agent,
                                        "Source Type": record.source_type,
                                        "Intent Type": record.intent_type,
                                        "Sender": record.sender,
                                        "Topic": record.topic,
                                        "Timestamp": record.timestamp.isoformat(),
                                        "Extracted Fields": json.loads(record.extracted_fields) if record.extracted_fields else None,
                                        "Metadata": json.loads(record.extra_metadata) if record.extra_metadata else None
                                    })
                    else:
                        st.info("No records found.")
            except Exception as e:
                st.error(f"Error loading records: {e}")

def display_search_results(results):
    """Display search results in a structured format."""
    if not results:
        st.info("No results found.")
        return

    data = []
    for entry in results:
        data.append({
            "ID": entry.id,
            "Thread": entry.thread_id,
            "Agent": entry.processing_agent,
            "Source": entry.source_type,
            "Intent": entry.intent_type,
            "Sender": entry.sender or "N/A",
            "Topic": entry.topic or "N/A",
            "Timestamp": entry.timestamp.strftime("%Y-%m-%d %H:%M")
        })

    df = pd.DataFrame(data)
    st.dataframe(df, use_container_width=True)

    # Show detailed view for selected record
    if len(results) > 0:
        st.markdown("### Record Details")
        selected_id = st.selectbox("Select a record to view details:", df["ID"])
        if selected_id:
            entry = next((r for r in results if r.id == selected_id), None)
            if entry:
                st.json({
                    "ID": entry.id,
                    "Thread ID": entry.thread_id,
                    "Processing Agent": entry.processing_agent,
                    "Source Type": entry.source_type,
                    "Intent Type": entry.intent_type,
                    "Sender": entry.sender,
                    "Topic": entry.topic,
                    "Timestamp": entry.timestamp.isoformat(),
                    "Extracted Fields": entry.extracted_fields,
                    "Metadata": entry.metadata
                })

def show_status_page():
    """Display system status and configuration."""

    st.markdown("## ⚙️ System Status")

    # Configuration status
    col1, col2 = st.columns(2)

    with col1:
        st.markdown("### 🔧 Configuration")

        config_status = {
            "OpenAI API Key": "Configured" if config.OPENAI_API_KEY else "Not Set",
            "OpenAI Model": config.OPENAI_MODEL,
            "Database URL": config.DATABASE_URL,
            "Max File Size": f"{config.MAX_FILE_SIZE_MB} MB",
            "Log Level": config.LOG_LEVEL
        }

        for key, value in config_status.items():
            st.markdown(f"**{key}:** {value}")

    with col2:
        st.markdown("### System Metrics")

        try:
            with memory.get_session() as session:
                from shared_memory import MemoryRecord
                total_records = session.query(MemoryRecord).count()

                # Recent activity
                recent_records = session.query(MemoryRecord).filter(
                    MemoryRecord.timestamp >= datetime.utcnow() - timedelta(hours=24)
                ).count()

                # Thread count
                thread_count = session.query(MemoryRecord.thread_id).distinct().count()
        except:
            total_records = 0
            recent_records = 0
            thread_count = 0

        st.metric("Total Records", total_records)
        st.metric("Last 24 Hours", recent_records)
        st.metric("Active Threads", thread_count)
        st.metric("Supported Formats", len(config.FORMAT_TYPES))

    # Supported formats and intents
    st.markdown("### Supported Formats & Intents")

    col3, col4 = st.columns(2)

    with col3:
        st.markdown("**Supported Formats:**")
        for fmt in config.FORMAT_TYPES:
            st.markdown(f"- {fmt}")

    with col4:
        st.markdown("**Supported Intents:**")
        for intent in config.INTENT_TYPES:
            st.markdown(f"- {intent}")

    # Database cleanup
    st.markdown("### 🧹 Maintenance")
    if st.button("🗑️ Clean Old Records (30+ days)"):
        try:
            deleted_count = memory.cleanup_old_records(30)
            st.success(f"Cleaned up {deleted_count} old records.")
        except Exception as e:
            st.error(f"Cleanup failed: {e}")

if __name__ == "__main__":
    main()
