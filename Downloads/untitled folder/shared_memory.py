"""Shared memory module for maintaining context across agents."""

import json
import uuid
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any
from sqlalchemy import create_engine, Column, String, DateTime, Text, Integer
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from pydantic import BaseModel

from config import config

Base = declarative_base()

class MemoryRecord(Base):
    """SQLAlchemy model for memory records."""
    __tablename__ = "memory_records"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    thread_id = Column(String, nullable=False, index=True)
    source_type = Column(String, nullable=False)  # pdf, json, email
    intent_type = Column(String, nullable=False)  # invoice, rfq, etc.
    sender = Column(String, nullable=True)
    topic = Column(String, nullable=True)
    extracted_fields = Column(Text, nullable=True)  # JSON string
    raw_content_hash = Column(String, nullable=True)
    processing_agent = Column(String, nullable=False)
    timestamp = Column(DateTime, default=datetime.utcnow)
    extra_metadata = Column(Text, nullable=True)  # Additional JSON metadata

class MemoryEntry(BaseModel):
    """Pydantic model for memory entries."""
    id: Optional[str] = None
    thread_id: str
    source_type: str
    intent_type: str
    sender: Optional[str] = None
    topic: Optional[str] = None
    extracted_fields: Optional[Dict[str, Any]] = None
    raw_content_hash: Optional[str] = None
    processing_agent: str
    timestamp: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None

class SharedMemory:
    """Shared memory manager for the multi-agent system."""

    def __init__(self, database_url: str = None):
        self.database_url = database_url or config.DATABASE_URL
        self.engine = create_engine(self.database_url)
        Base.metadata.create_all(self.engine)
        self.SessionLocal = sessionmaker(bind=self.engine)

    def get_session(self) -> Session:
        """Get a database session."""
        return self.SessionLocal()

    def store_memory(self, entry: MemoryEntry) -> str:
        """Store a memory entry and return its ID."""
        with self.get_session() as session:
            # Convert to SQLAlchemy model
            record = MemoryRecord(
                id=entry.id or str(uuid.uuid4()),
                thread_id=entry.thread_id,
                source_type=entry.source_type,
                intent_type=entry.intent_type,
                sender=entry.sender,
                topic=entry.topic,
                extracted_fields=json.dumps(entry.extracted_fields) if entry.extracted_fields else None,
                raw_content_hash=entry.raw_content_hash,
                processing_agent=entry.processing_agent,
                timestamp=entry.timestamp or datetime.utcnow(),
                extra_metadata=json.dumps(entry.metadata) if entry.metadata else None
            )

            session.add(record)
            session.commit()
            return record.id

    def get_memory_by_id(self, memory_id: str) -> Optional[MemoryEntry]:
        """Retrieve a memory entry by ID."""
        with self.get_session() as session:
            record = session.query(MemoryRecord).filter(MemoryRecord.id == memory_id).first()
            if record:
                return self._record_to_entry(record)
            return None

    def get_thread_history(self, thread_id: str, limit: int = 10) -> List[MemoryEntry]:
        """Get memory history for a specific thread."""
        with self.get_session() as session:
            records = session.query(MemoryRecord)\
                .filter(MemoryRecord.thread_id == thread_id)\
                .order_by(MemoryRecord.timestamp.desc())\
                .limit(limit)\
                .all()

            return [self._record_to_entry(record) for record in records]

    def search_by_sender(self, sender: str, limit: int = 10) -> List[MemoryEntry]:
        """Search memory entries by sender."""
        with self.get_session() as session:
            records = session.query(MemoryRecord)\
                .filter(MemoryRecord.sender.ilike(f"%{sender}%"))\
                .order_by(MemoryRecord.timestamp.desc())\
                .limit(limit)\
                .all()

            return [self._record_to_entry(record) for record in records]

    def search_by_intent(self, intent_type: str, limit: int = 10) -> List[MemoryEntry]:
        """Search memory entries by intent type."""
        with self.get_session() as session:
            records = session.query(MemoryRecord)\
                .filter(MemoryRecord.intent_type == intent_type)\
                .order_by(MemoryRecord.timestamp.desc())\
                .limit(limit)\
                .all()

            return [self._record_to_entry(record) for record in records]

    def cleanup_old_records(self, days: int = None) -> int:
        """Clean up old memory records."""
        days = days or config.MEMORY_RETENTION_DAYS
        cutoff_date = datetime.utcnow() - timedelta(days=days)

        with self.get_session() as session:
            deleted_count = session.query(MemoryRecord)\
                .filter(MemoryRecord.timestamp < cutoff_date)\
                .delete()
            session.commit()
            return deleted_count

    def _record_to_entry(self, record: MemoryRecord) -> MemoryEntry:
        """Convert SQLAlchemy record to Pydantic model."""
        return MemoryEntry(
            id=record.id,
            thread_id=record.thread_id,
            source_type=record.source_type,
            intent_type=record.intent_type,
            sender=record.sender,
            topic=record.topic,
            extracted_fields=json.loads(record.extracted_fields) if record.extracted_fields else None,
            raw_content_hash=record.raw_content_hash,
            processing_agent=record.processing_agent,
            timestamp=record.timestamp,
            metadata=json.loads(record.extra_metadata) if record.extra_metadata else None
        )

# Global memory instance
memory = SharedMemory()
