# Multi-Agent AI System

A sophisticated multi-agent AI system that accepts input in PDF, JSON, or Email (text) format, classifies the format and intent, and routes it to the appropriate specialized agent. The system maintains shared context to enable chaining and traceability across all processing stages.

## 🏗️ System Architecture

### Core Components

1. **Classifier Agent** - Central orchestrator that classifies format and intent
2. **JSON Agent** - Handles structured JSON data processing and validation
3. **Email Agent** - Processes email content and extracts metadata
4. **Shared Memory Module** - SQLite-based storage for context and traceability
5. **Main Orchestrator** - Entry point that coordinates all agents

### Agent Workflow

```
Input → Classifier Agent → Specialized Agent → Shared Memory → Results
```

## 🚀 Features

### Format Detection
- **PDF**: Text extraction and content analysis
- **JSON**: Structure validation and data extraction
- **Email**: Metadata extraction and content analysis
- **Text**: General text processing with format detection

### Intent Classification
- Invoice processing
- Request for Quote (RFQ)
- Complaint handling
- Regulation compliance
- General inquiries
- Order processing
- Support requests

### Shared Context Management
- Thread-based conversation tracking
- Historical data analysis
- Anomaly detection
- Cross-agent information sharing

## 📦 Installation

1. **Clone or download the project**
2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment variables** (create `.env` file):
   ```env
   OPENAI_API_KEY=your_openai_api_key_here
   OPENAI_MODEL=gpt-3.5-turbo
   DATABASE_URL=sqlite:///agent_memory.db
   LOG_LEVEL=INFO
   ```

## 🔧 Configuration

Edit `config.py` to customize:
- Supported file formats
- Intent types
- Processing timeouts
- Memory retention settings
- Logging configuration

## 💻 Usage

### Basic Usage

```python
from main import orchestrator

# Process a file
result = orchestrator.process_file("path/to/your/file.pdf")

# Process raw content
result = orchestrator.process_input("Your email content here", thread_id="conversation-1")

# Get thread summary
summary = orchestrator.get_thread_summary("conversation-1")
```

### Command Line Usage

```bash
# Process a file
python main.py examples/sample_invoice.json

# Process with specific thread ID
python main.py examples/sample_email.txt thread-123
```

### API Integration

The system can be easily integrated into web applications:

```python
from fastapi import FastAPI, UploadFile
from main import orchestrator

app = FastAPI()

@app.post("/process")
async def process_document(file: UploadFile, thread_id: str = None):
    # Save uploaded file temporarily
    content = await file.read()
    
    # Process with orchestrator
    result = orchestrator.process_input(
        content.decode(), 
        thread_id=thread_id,
        source_hint=file.content_type
    )
    
    return result
```

## 📁 Project Structure

```
multi-agent-system/
├── agents/
│   ├── __init__.py
│   ├── base_agent.py          # Base class for all agents
│   ├── classifier_agent.py    # Format and intent classification
│   ├── json_agent.py          # JSON processing specialist
│   └── email_agent.py         # Email processing specialist
├── utils/
│   ├── __init__.py
│   ├── llm_client.py          # OpenAI integration wrapper
│   └── file_handlers.py       # File processing utilities
├── examples/
│   ├── sample_invoice.json    # Example invoice data
│   ├── sample_rfq.json        # Example RFQ data
│   ├── sample_email.txt       # Example email
│   └── sample_complaint.txt   # Example complaint
├── config.py                  # Configuration settings
├── shared_memory.py           # Memory management
├── main.py                    # Main orchestrator
├── requirements.txt           # Dependencies
└── README.md                  # This file
```

## 🔍 Example Workflows

### 1. Invoice Processing
```python
# Input: JSON invoice file
result = orchestrator.process_file("examples/sample_invoice.json")

# Output: Structured data with validation results
{
  "success": true,
  "classification": {"format": "json", "intent": "invoice"},
  "processing_results": {
    "extracted_data": {...},
    "validation_results": {"is_valid": true},
    "anomalies": []
  }
}
```

### 2. Email Analysis
```python
# Input: Email content
result = orchestrator.process_file("examples/sample_email.txt")

# Output: Email metadata and urgency analysis
{
  "success": true,
  "classification": {"format": "email", "intent": "complaint"},
  "processing_results": {
    "email_metadata": {"sender": "<EMAIL>"},
    "urgency_analysis": {"level": "high"},
    "action_items": [...]
  }
}
```

## 🧠 Memory and Context

The system maintains shared context through:

- **Thread Tracking**: All related communications grouped by thread ID
- **Historical Analysis**: Anomaly detection based on historical patterns
- **Cross-Agent Sharing**: Information flows between agents
- **Traceability**: Complete audit trail of all processing steps

### Memory Operations

```python
# Search by sender
results = orchestrator.search_by_criteria({"sender": "<EMAIL>"})

# Get thread summary
summary = orchestrator.get_thread_summary("thread-123")

# Access memory directly
from shared_memory import memory
entries = memory.get_thread_history("thread-123", limit=10)
```

## 🔧 Customization

### Adding New Intent Types

1. Update `config.py`:
   ```python
   INTENT_TYPES = [..., "new_intent"]
   ```

2. Add processing logic in relevant agents
3. Update classification prompts in `llm_client.py`

### Adding New Agents

1. Create new agent class inheriting from `BaseAgent`
2. Implement the `process()` method
3. Register in `main.py` orchestrator
4. Update routing logic in `classifier_agent.py`

### Custom Schemas

Add new schemas in `json_agent.py`:
```python
self.schemas["new_type"] = {
    "type": "object",
    "properties": {...}
}
```

## 🚨 Error Handling

The system includes comprehensive error handling:
- Input validation
- LLM fallback mechanisms
- Graceful degradation
- Detailed error logging

## 📊 Monitoring and Logging

- Structured logging with configurable levels
- Processing metrics and statistics
- Memory usage tracking
- Agent performance monitoring

## 🔒 Security Considerations

- Input sanitization
- File size limits
- Content validation
- API key protection

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For issues and questions:
1. Check the examples in the `examples/` directory
2. Review the configuration in `config.py`
3. Check logs in `agent_system.log`
4. Ensure OpenAI API key is properly configured

## 🔮 Future Enhancements

- Support for more file formats (Word, Excel, etc.)
- Real-time processing with WebSocket support
- Advanced ML models for classification
- Integration with external CRM systems
- Multi-language support
- Advanced analytics dashboard
