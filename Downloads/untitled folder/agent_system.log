2025-05-28 19:05:28,699 - orchestrator - INFO - Multi-agent orchestrator initialized
2025-05-28 19:09:15,530 - orchestrator - INFO - Multi-agent orchestrator initialized
2025-05-28 19:17:44,433 - orchestrator - INFO - Multi-agent orchestrator initialized
2025-05-28 19:20:17,775 - orchestrator - INFO - Processing input for thread e7718742
2025-05-28 19:20:19,039 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 401 Unauthorized"
2025-05-28 19:20:19,043 - llm_client - ERROR - LLM classification failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-28 19:20:19,044 - agent.classifier - INFO - Applying source type hint: pdf
2025-05-28 19:20:19,055 - agent.classifier - INFO - Logged processing result to memory: ********-d588-42c9-aba4-714e4f23041f
2025-05-28 19:20:19,055 - agent.classifier - INFO - Classified content: format=pdf, intent=complaint, confidence=0.6, routing=email_agent
2025-05-28 19:20:19,352 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 401 Unauthorized"
2025-05-28 19:20:19,353 - llm_client - ERROR - Email analysis failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-28 19:20:19,369 - agent.email - INFO - Logged processing result to memory: ********-6f68-4af6-b45c-0f40f2212055
2025-05-28 19:20:19,369 - agent.email - INFO - Processed email: intent=complaint, urgency=high, actions=1, sender=None
2025-05-28 19:20:19,369 - orchestrator - INFO - Successfully processed input for thread e7718742
2025-05-28 19:22:47,265 - orchestrator - INFO - Processing input for thread c572b338
2025-05-28 19:22:47,622 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 401 Unauthorized"
2025-05-28 19:22:47,628 - llm_client - ERROR - LLM classification failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-28 19:22:47,635 - agent.classifier - INFO - Logged processing result to memory: 9b4247e8-3aed-4136-8548-a57c9cedf7c8
2025-05-28 19:22:47,635 - agent.classifier - INFO - Classified content: format=text, intent=other, confidence=0.3, routing=email_agent
2025-05-28 19:22:47,919 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 401 Unauthorized"
2025-05-28 19:22:47,921 - llm_client - ERROR - Email analysis failed: Error code: 401 - {'error': {'message': 'Incorrect API key provided: your_ope************here. You can find your API key at https://platform.openai.com/account/api-keys.', 'type': 'invalid_request_error', 'param': None, 'code': 'invalid_api_key'}}
2025-05-28 19:22:47,927 - agent.email - INFO - Logged processing result to memory: ed1b3459-4123-477e-95f0-06f2c265bd7d
2025-05-28 19:22:47,928 - agent.email - INFO - Processed email: intent=other, urgency=low, actions=0, sender=None
2025-05-28 19:22:47,928 - orchestrator - INFO - Successfully processed input for thread c572b338
2025-05-28 19:26:30,997 - orchestrator - INFO - Multi-agent orchestrator initialized
2025-05-28 19:26:50,220 - orchestrator - INFO - Processing input for thread 39f6a9d5
2025-05-28 19:26:51,148 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-28 19:26:51,149 - openai._base_client - INFO - Retrying request to /chat/completions in 0.434593 seconds
2025-05-28 19:26:52,365 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-28 19:26:52,366 - openai._base_client - INFO - Retrying request to /chat/completions in 0.972019 seconds
2025-05-28 19:26:54,836 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-28 19:26:54,840 - llm_client - ERROR - LLM classification failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-05-28 19:26:54,841 - agent.classifier - INFO - Applying source type hint: pdf
2025-05-28 19:26:54,852 - agent.classifier - INFO - Logged processing result to memory: 692da479-9a63-489a-9b87-ca05cf143067
2025-05-28 19:26:54,852 - agent.classifier - INFO - Classified content: format=pdf, intent=complaint, confidence=0.6, routing=email_agent
2025-05-28 19:26:55,604 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-28 19:26:55,605 - openai._base_client - INFO - Retrying request to /chat/completions in 0.452315 seconds
2025-05-28 19:26:56,367 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-28 19:26:56,368 - openai._base_client - INFO - Retrying request to /chat/completions in 0.750140 seconds
2025-05-28 19:26:57,439 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-28 19:26:57,441 - llm_client - ERROR - Email analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-05-28 19:26:57,461 - agent.email - INFO - Logged processing result to memory: dde353b3-2531-41d1-8963-8bc8bd87fb27
2025-05-28 19:26:57,462 - agent.email - INFO - Processed email: intent=complaint, urgency=high, actions=2, sender=None
2025-05-28 19:26:57,462 - orchestrator - INFO - Successfully processed input for thread 39f6a9d5
2025-05-28 19:30:45,357 - orchestrator - INFO - Multi-agent orchestrator initialized
2025-05-28 19:31:22,987 - orchestrator - INFO - Processing input for thread 0dd29800
2025-05-28 19:31:23,796 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-28 19:31:23,797 - openai._base_client - INFO - Retrying request to /chat/completions in 0.494877 seconds
2025-05-28 19:31:24,591 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-28 19:31:24,592 - openai._base_client - INFO - Retrying request to /chat/completions in 0.903956 seconds
2025-05-28 19:31:25,795 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-28 19:31:25,799 - llm_client - ERROR - LLM classification failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-05-28 19:31:25,799 - agent.classifier - INFO - Applying source type hint: pdf
2025-05-28 19:31:25,812 - agent.classifier - INFO - Logged processing result to memory: 5743bc88-c57b-49db-9dba-3f6dbd3297a5
2025-05-28 19:31:25,812 - agent.classifier - INFO - Classified content: format=pdf, intent=other, confidence=0.6, routing=email_agent
2025-05-28 19:31:26,100 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-28 19:31:26,100 - openai._base_client - INFO - Retrying request to /chat/completions in 0.442886 seconds
2025-05-28 19:31:26,841 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-28 19:31:26,841 - openai._base_client - INFO - Retrying request to /chat/completions in 0.971342 seconds
2025-05-28 19:31:28,116 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-28 19:31:28,117 - llm_client - ERROR - Email analysis failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-05-28 19:31:28,130 - agent.email - INFO - Logged processing result to memory: d7a96117-b007-4960-a6e2-1f10acb6f080
2025-05-28 19:31:28,131 - agent.email - INFO - Processed email: intent=other, urgency=medium, actions=3, sender=None
2025-05-28 19:31:28,131 - orchestrator - INFO - Successfully processed input for thread 0dd29800
2025-05-28 19:35:31,936 - orchestrator - INFO - Multi-agent orchestrator initialized
2025-05-28 19:36:16,767 - orchestrator - INFO - Processing input for thread d1e191e2
2025-05-28 19:36:18,115 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-28 19:36:18,116 - openai._base_client - INFO - Retrying request to /chat/completions in 0.429697 seconds
2025-05-28 19:36:18,935 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-28 19:36:18,935 - openai._base_client - INFO - Retrying request to /chat/completions in 0.957264 seconds
2025-05-28 19:36:20,280 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-28 19:36:20,282 - llm_client - ERROR - LLM classification failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-05-28 19:36:20,282 - agent.classifier - INFO - Applying source type hint: pdf
2025-05-28 19:36:20,293 - agent.classifier - INFO - Logged processing result to memory: 8a87d93a-0b9b-43cd-bcf3-82775e5ebe7f
2025-05-28 19:36:20,293 - agent.classifier - INFO - Classified content: format=pdf, intent=other, confidence=0.6, routing=json_agent
2025-05-28 19:36:20,294 - agent.json - ERROR - Error in json_agent: Invalid JSON content: Expecting value: line 1 column 1 (char 0)
2025-05-28 19:36:20,294 - orchestrator - INFO - Successfully processed input for thread d1e191e2
2025-05-28 19:37:11,198 - orchestrator - INFO - Multi-agent orchestrator initialized
2025-05-28 19:37:47,375 - orchestrator - INFO - Processing input for thread 14d0ddcb
2025-05-28 19:37:47,910 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-28 19:37:47,911 - openai._base_client - INFO - Retrying request to /chat/completions in 0.486995 seconds
2025-05-28 19:37:48,709 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-28 19:37:48,710 - openai._base_client - INFO - Retrying request to /chat/completions in 0.904896 seconds
2025-05-28 19:37:50,506 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-28 19:37:50,511 - llm_client - ERROR - LLM classification failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-05-28 19:37:50,511 - agent.classifier - INFO - Applying source type hint: pdf
2025-05-28 19:37:50,525 - agent.classifier - INFO - Logged processing result to memory: 02d1db02-261a-4ab8-b2e8-168a2407316d
2025-05-28 19:37:50,525 - agent.classifier - INFO - Classified content: format=pdf, intent=other, confidence=0.6, routing=json_agent
2025-05-28 19:37:50,525 - agent.json - ERROR - Error in json_agent: Invalid JSON content: Expecting value: line 1 column 1 (char 0)
2025-05-28 19:37:50,525 - orchestrator - INFO - Successfully processed input for thread 14d0ddcb
2025-05-28 19:51:30,924 - orchestrator - INFO - Multi-agent orchestrator initialized
2025-05-28 19:51:48,080 - orchestrator - INFO - Processing input for thread e805ec46
2025-05-28 19:51:48,684 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-28 19:51:48,686 - openai._base_client - INFO - Retrying request to /chat/completions in 0.415404 seconds
2025-05-28 19:51:49,490 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-28 19:51:49,491 - openai._base_client - INFO - Retrying request to /chat/completions in 0.794720 seconds
2025-05-28 19:51:50,798 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-28 19:51:50,802 - llm_client - ERROR - LLM classification failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-05-28 19:51:50,802 - agent.classifier - INFO - Applying source type hint: pdf
2025-05-28 19:51:50,820 - agent.classifier - INFO - Logged processing result to memory: a8c597af-738d-440b-88b1-3494f5eabe68
2025-05-28 19:51:50,820 - agent.classifier - INFO - Classified content: format=pdf, intent=other, confidence=0.6, routing=json_agent
2025-05-28 19:51:50,820 - agent.json - ERROR - Error in json_agent: Invalid JSON content: Expecting value: line 1 column 1 (char 0)
2025-05-28 19:51:50,820 - orchestrator - INFO - Successfully processed input for thread e805ec46
2025-05-28 19:52:13,886 - orchestrator - INFO - Processing input for thread 859bbafc
2025-05-28 19:52:14,238 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-28 19:52:14,239 - openai._base_client - INFO - Retrying request to /chat/completions in 0.447819 seconds
2025-05-28 19:52:15,363 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-28 19:52:15,364 - openai._base_client - INFO - Retrying request to /chat/completions in 0.890243 seconds
2025-05-28 19:52:16,925 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 429 Too Many Requests"
2025-05-28 19:52:16,930 - llm_client - ERROR - LLM classification failed: Error code: 429 - {'error': {'message': 'You exceeded your current quota, please check your plan and billing details. For more information on this error, read the docs: https://platform.openai.com/docs/guides/error-codes/api-errors.', 'type': 'insufficient_quota', 'param': None, 'code': 'insufficient_quota'}}
2025-05-28 19:52:16,931 - agent.classifier - INFO - Applying source type hint: pdf
2025-05-28 19:52:16,964 - agent.classifier - INFO - Logged processing result to memory: 3519f70e-897f-4412-bfcb-4d4615012fb8
2025-05-28 19:52:16,964 - agent.classifier - INFO - Classified content: format=pdf, intent=complaint, confidence=0.6, routing=json_agent
2025-05-28 19:52:16,965 - agent.json - ERROR - Error in json_agent: Invalid JSON content: Expecting value: line 1 column 1 (char 0)
2025-05-28 19:52:16,965 - orchestrator - INFO - Successfully processed input for thread 859bbafc
